<!--
 * @Author: zhaoxm
 * @Date: 2022-04-15 11:01:52
 * @LastEditTime: 2022-11-15 23:30:53
-->

<script>
export default {
  props: {
    content: {
      type: String,
      default: "提示内容",
      require: true,
    },
    tag:{
      type: String,
      default: "span",
    }
  },
  data() {
    return {
      disabled:false
    }
  },
  methods:{
    handleMouseEnter (event) {
      const { target } = event
      const range = document.createRange()
      range.setStart(target, 0)
      range.setEnd(target, target.childNodes.length)
      const rangeWidth = Number.parseInt(range.getBoundingClientRect().width, 10)
      this.disabled = !((rangeWidth > target.offsetWidth || target.scrollWidth > target.offsetWidth))
    },
  },
  render(h) {
    return  h("el-tooltip", {
      props:{
        effect:"dark",
        placement:"top",
        disabled:this.disabled,
        content:this.content,
        ...this.$attrs,
      },
      class:"text-overflow"
    }, [
      h(this.tag, {
        on:{
          mouseenter:this.handleMouseEnter
        }
      }, this.$slots.default),
    ])
  },
}
</script>