<template>
  <div class="index-right">
    <el-empty
      v-show="isEmpty"
      description="请在左侧选择勾选数据哦~"
    />
    <div v-show="!isEmpty">
      <div class="content-right-header flex mb-16px">
        <div class="flex index-right-header_text flex-1">
          <span>已选</span>
          <div
            v-if="userCount"
            class="flex"
          >
            <span class="active">{{ userCount }}</span>
            <span>人</span>
          </div>
          <div
            v-if="deptCount"
            class="flex"
          >
            <span v-if="userCount">，</span>
            <span class="active">{{ deptCount }}</span>
            <span>部门</span>
          </div>
          <div
            v-if="roleCount"
            class="flex"
          >
            <span v-if="userCount || deptCount">，</span>
            <span class="active">{{ roleCount }}</span>
            <span>角色</span>
          </div>
        </div>
        <el-button
          type="text"
          v-if="showClearBtn"
          class="clear"
          @click="handleClearAllBtnClick"
        >
          清空
        </el-button>
      </div>
      <ul
        v-show="!isEmpty"
        ref="scroll-ul"
        @scroll="onScroll"
        class="pr-16px zui__scrollbar-y"
      >
        <template v-for="(list,key) in selectData">
          <template v-if="key === 'user'">
            <li
              v-for="(item,index) in selectUser"
              :key="key+index"
              class="flex"
            >
              <z-omit-words-tooltip
                :content="item|name"
                style="width:230px"
                class="text"
              >{{item|name}}</z-omit-words-tooltip>
              <i
                v-show="!item.disabled"
                class="el-icon-close close"
                @click="handleItemDelClick(key,item)"
              />
            </li>
          </template>
          <template v-else>
            <li
              v-for="(item,index) in selectData[key]"
              :key="key+index"
              class="flex"
            >
              <z-omit-words-tooltip
                :content="item|name"
                style="width:230px"
                class="text"
              >{{item|name}}</z-omit-words-tooltip>
              <i
                v-show="!item.disabled"
                class="el-icon-close close"
                @click="handleItemDelClick(key,item)"
              />
            </li>
          </template>
        </template>
      </ul>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from "~/utils/index.js";
import { DEPARTMENT, USER, ROLE } from "./config";
export default {
  filters: {
    name(data) {
      const { name, roleName, roleId, userId, cellPhone } = data;
      const cname = name || roleName;
      if (roleId) {
        return `${cname}（角色全员）`;
      }
      if (userId) {
        if (cellPhone) return `${cname}（${cellPhone}）`;
        return `${cname}`;
      }
      return `${cname}（部门全员）`;
    },
  },
  inject: ["selectData", "$appSelectUserRef"],
  data(){
    return {
      loadSize:20
    }
  },
  watch:{
    async "$appSelectUserRef.value" (newValue){
      if(newValue) return 
      this.loadSize = 20
    }
  },
  computed: {
    selectUser(){
      const users = this.selectData.user || []
      const newUsers = cloneDeep(users)
      if(newUsers.length) {
        newUsers.length = Math.min(this.loadSize,users.length)
      }
      return newUsers
    },
    isEmpty() {
      let empty = true;
      for (const item of Object.values(this.selectData)) {
        if (item.length) {
          empty = false;
          break;
        }
      }
      return empty;
    },
    isDisabledUser() {
      return this.selectData[USER].some((item) => item.disabled);
    },
    showClearBtn() {
      const show = false;
      // 如果有一个 选择的人员 不是禁用状态 ，就展示 清空按钮 ， 如果当前选择的人员全部是禁用状态 ， 则不需要展示清空按钮
      for (const item of this.selectData[USER]) {
        if (!item.disabled) {
          return true;
        }
      }

      for (const item of this.selectData[DEPARTMENT]) {
        if (!item.disabled) {
          return true;
        }
      }

      for (const item of this.selectData[ROLE]) {
        if (!item.disabled) {
          return true;
        }
      }

      return show;
    },
    userCount() {
      return this.selectData[USER].length;
    },
    deptCount() {
      return this.selectData[DEPARTMENT].length;
    },
    roleCount() {
      return this.selectData[ROLE].length;
    },
  },
  methods: {
    onScroll(){
      const container = this.$refs["scroll-ul"];
      const isAtBottom = container.scrollTop + container.clientHeight === container.scrollHeight;
      if (!isAtBottom) return 
      this.loadSize +=20
    },
    // 点击单独删除按钮
    handleItemDelClick(key, item) {
      // 删除角色
      if (key === ROLE) {
        return this.$appSelectUserRef.childIndexLeftRef(
          "index-left-role-list",
          "handleItemDelClick",
          item
        );
      }

      // 删除部门
      if (key === DEPARTMENT) {
        return this.$appSelectUserRef.childIndexLeftRef(
          "index-left-tree",
          "handleItemDelClick",
          item
        );
      }

      // 删除人员
      this.$appSelectUserRef.childIndexLeftRef(
        "index-left-user",
        "handlePersonClick",
        item
      );
    },
    // 点击全部清空按钮
    handleClearAllBtnClick() {
      // 如果有禁用的用户 ， 现在这个场景 只有预算规则人员选择， 再次打开弹框 选择人员禁用 才会用到的逻辑
      if (this.isDisabledUser) {
        // 把非禁用的全部删除掉 ， 已经选择的 并且是禁用状态 点击清空 按钮不做处理
        for (const item of this.selectData[USER]) {
          if (!item.disabled) this.handleItemDelClick(USER, item);
        }
      } else {
        this.$appSelectUserRef.clearField();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.index-right {
  position: relative;
  &-header_text {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #6a6f7f;
    letter-spacing: 0;
    line-height: 14px;
    .active {
      color: #ff9500;
      padding: 0 4px;
    }
  }
  .clear {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    padding: 13px;
  }
  ul {
    max-height: 354px;
  }
  li {
    width: 100%;
    background: #f4f4f4;
    border-radius: 4px;
    padding: 12px 0 12px 10px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 14px;
    color: #6a6f7f;
    letter-spacing: 0;
    line-height: 14px;
    margin-bottom: 10px;
    position: relative;
    .icon {
      width: 16px;
      height: 16px;
      display: block;
      // background: url("../../assets/images/staff.png") no-repeat center;
      margin-right: 10px;
      background-size: cover;
    }
    .text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 90%;
    }
    .close {
      position: absolute;
      right: 0;
      cursor: pointer;
      top: 0;
      padding: 12px;
    }
  }
}
</style>