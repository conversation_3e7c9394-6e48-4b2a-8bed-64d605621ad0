import { version, name } from "~/package.json"
import { rootConfig,setConfig } from "./config"
import packages from "../packages/index"
import '../packages/style/base.scss'
import 'uno.css'

const install = (app) => {
  Object.keys(packages).forEach(key => {
    const componentInstance = packages[key]
    app.use(componentInstance)
  })
}

export default {
  install,
  version,
  name,
  setConfig,
  rootConfig
}