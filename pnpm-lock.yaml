lockfileVersion: 5.4

specifiers:
  '@iconify-json/ep': ^1.1.8
  '@iconify-json/fa6-solid': ^1.1.7
  '@unocss/preset-attributify': ^0.46.5
  '@unocss/preset-icons': ^0.46.5
  '@unocss/preset-uno': ^0.46.5
  '@unocss/preset-wind': ^0.46.5
  '@unocss/reset': ^0.46.5
  '@unocss/vite': ^0.46.5
  axios: ^1.1.3
  element-ui: 2.15.7
  plop: ^3.1.1
  sass: ^1.56.1
  unocss: ^0.46.5
  vite: ^3.2.3
  vite-plugin-vue2: ^2.0.2
  vue: 2.6.11
  vue-router: 3.5.3
  vue-template-compiler: 2.6.11
  vue-virtual-scroll-list: 2.3.5

dependencies:
  vue-virtual-scroll-list: registry.npmmirror.com/vue-virtual-scroll-list/2.3.5

devDependencies:
  '@iconify-json/ep': registry.npmmirror.com/@iconify-json/ep/1.1.8
  '@iconify-json/fa6-solid': registry.npmmirror.com/@iconify-json/fa6-solid/1.1.7
  '@unocss/preset-attributify': registry.npmmirror.com/@unocss/preset-attributify/0.46.5
  '@unocss/preset-icons': registry.npmmirror.com/@unocss/preset-icons/0.46.5
  '@unocss/preset-uno': registry.npmmirror.com/@unocss/preset-uno/0.46.5
  '@unocss/preset-wind': registry.npmmirror.com/@unocss/preset-wind/0.46.5
  '@unocss/reset': registry.npmmirror.com/@unocss/reset/0.46.5
  '@unocss/vite': registry.npmmirror.com/@unocss/vite/0.46.5_vite@3.2.3
  axios: registry.npmmirror.com/axios/1.1.3
  element-ui: 2.15.7_vue@2.6.11
  plop: registry.npmmirror.com/plop/3.1.1
  sass: 1.56.1
  unocss: registry.npmmirror.com/unocss/0.46.5_vite@3.2.3
  vite: 3.2.3_sass@1.56.1
  vite-plugin-vue2: 2.0.2_lauxldphyul6wmyri5znbggen4
  vue: 2.6.11
  vue-router: 3.5.3_vue@2.6.11
  vue-template-compiler: 2.6.11

packages:

  /@babel/code-frame/7.18.6:
    resolution: {integrity: sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.18.6
    dev: true

  /@babel/compat-data/7.20.1:
    resolution: {integrity: sha512-EWZ4mE2diW3QALKvDMiXnbZpRvlj+nayZ112nK93SnhqOtpdsbVD4W+2tEoT3YNBAG9RBR0ISY758ZkOgsn6pQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/core/7.20.2:
    resolution: {integrity: sha512-w7DbG8DtMrJcFOi4VrLm+8QM4az8Mo+PuLBKLp2zrYRCow8W/f9xiXm5sN53C8HksCyDQwCKha9JiDoIyPjT2g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': registry.npmmirror.com/@ampproject/remapping/2.2.0
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.20.4
      '@babel/helper-compilation-targets': 7.20.0_@babel+core@7.20.2
      '@babel/helper-module-transforms': 7.20.2
      '@babel/helpers': 7.20.1
      '@babel/parser': 7.20.3
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.1
      semver: 6.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/generator/7.20.4:
    resolution: {integrity: sha512-luCf7yk/cm7yab6CAW1aiFnmEfBJplb/JojV56MYEK7ziWfGmFlTfmL9Ehwfy4gFhbjBfWO1wj7/TuSbVNEEtA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
      '@jridgewell/gen-mapping': registry.npmmirror.com/@jridgewell/gen-mapping/0.3.2
      jsesc: 2.5.2
    dev: true

  /@babel/helper-annotate-as-pure/7.18.6:
    resolution: {integrity: sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-compilation-targets/7.20.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/compat-data': 7.20.1
      '@babel/core': 7.20.2
      '@babel/helper-validator-option': 7.18.6
      browserslist: 4.21.4
      semver: 6.3.0
    dev: true

  /@babel/helper-create-class-features-plugin/7.20.2_@babel+core@7.20.2:
    resolution: {integrity: sha512-k22GoYRAHPYr9I+Gvy2ZQlAe5mGy8BqWst2wRt8cwIufWTxrsVshhIBvYNqC80N0GSFWTsqRVexOtfzlgOEDvA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-annotate-as-pure': 7.18.6
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-environment-visitor/7.18.9:
    resolution: {integrity: sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-function-name/7.19.0:
    resolution: {integrity: sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-hoist-variables/7.18.6:
    resolution: {integrity: sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-member-expression-to-functions/7.18.9:
    resolution: {integrity: sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-module-imports/7.18.6:
    resolution: {integrity: sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-module-transforms/7.20.2:
    resolution: {integrity: sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-module-imports': 7.18.6
      '@babel/helper-simple-access': 7.20.2
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/helper-validator-identifier': 7.19.1
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-optimise-call-expression/7.18.6:
    resolution: {integrity: sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-plugin-utils/7.20.2:
    resolution: {integrity: sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-replace-supers/7.19.1:
    resolution: {integrity: sha512-T7ahH7wV0Hfs46SFh5Jz3s0B6+o8g3c+7TMxu7xKfmHikg7EAZ3I2Qk9LFhjxXq8sL7UkP5JflezNwoZa8WvWw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-member-expression-to-functions': 7.18.9
      '@babel/helper-optimise-call-expression': 7.18.6
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/helper-simple-access/7.20.2:
    resolution: {integrity: sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-skip-transparent-expression-wrappers/7.20.0:
    resolution: {integrity: sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-split-export-declaration/7.18.6:
    resolution: {integrity: sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/helper-string-parser/7.19.4:
    resolution: {integrity: sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-identifier/7.19.1:
    resolution: {integrity: sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-option/7.18.6:
    resolution: {integrity: sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helpers/7.20.1:
    resolution: {integrity: sha512-J77mUVaDTUJFZ5BpP6mMn6OIl3rEWymk2ZxDBQJUG3P+PbmyMcF3bYWvz0ma69Af1oobDqT/iAsvzhB58xhQUg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.18.10
      '@babel/traverse': 7.20.1
      '@babel/types': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/highlight/7.18.6:
    resolution: {integrity: sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.19.1
      chalk: registry.npmmirror.com/chalk/2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/parser/7.20.3:
    resolution: {integrity: sha512-OP/s5a94frIPXwjzEcv5S/tpQfc6XhxYUnmWpgdqMWGgYCuErA3SzozaRAMQgSZWKeTJxht9aWAkUY+0UzvOFg==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.20.2
    dev: true

  /@babel/plugin-proposal-class-properties/7.18.6_@babel+core@7.20.2:
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-class-features-plugin': 7.20.2_@babel+core@7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-decorators/7.20.2_@babel+core@7.20.2:
    resolution: {integrity: sha512-nkBH96IBmgKnbHQ5gXFrcmez+Z9S2EIDKDQGp005ROqBigc88Tky4rzCnlP/lnlj245dCEQl4/YyV0V1kYh5dw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-class-features-plugin': 7.20.2_@babel+core@7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-replace-supers': 7.19.1
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/plugin-syntax-decorators': 7.19.0_@babel+core@7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/plugin-proposal-nullish-coalescing-operator/7.18.6_@babel+core@7.20.2:
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3_@babel+core@7.20.2
    dev: true

  /@babel/plugin-proposal-object-rest-spread/7.20.2_@babel+core@7.20.2:
    resolution: {integrity: sha512-Ks6uej9WFK+fvIMesSqbAto5dD8Dz4VuuFvGJFKgIGSkJuRGcrwGECPA1fDgQK3/DbExBJpEkTeYeB8geIFCSQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.20.1
      '@babel/core': 7.20.2
      '@babel/helper-compilation-targets': 7.20.0_@babel+core@7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-object-rest-spread': 7.8.3_@babel+core@7.20.2
      '@babel/plugin-transform-parameters': 7.20.3_@babel+core@7.20.2
    dev: true

  /@babel/plugin-proposal-optional-chaining/7.18.9_@babel+core@7.20.2:
    resolution: {integrity: sha512-v5nwt4IqBXihxGsW2QmCWMDS3B3bzGIk/EQVZz2ei7f3NJl8NzAJVvUmpDW5q1CRNY+Beb/k58UAH1Km1N411w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
      '@babel/plugin-syntax-optional-chaining': 7.8.3_@babel+core@7.20.2
    dev: true

  /@babel/plugin-syntax-decorators/7.19.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-xaBZUEDntt4faL1yN8oIFlhfXeQAWJW7CLKYsHTUqriCUbj8xOra8bfxxKGi/UwExPFBuPdH4XfHc9rGQhrVkQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-jsx/7.18.6_@babel+core@7.20.2:
    resolution: {integrity: sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3_@babel+core@7.20.2:
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-object-rest-spread/7.8.3_@babel+core@7.20.2:
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-optional-chaining/7.8.3_@babel+core@7.20.2:
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-syntax-typescript/7.20.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-rd9TkG+u1CExzS4SM1BlMEhMXwFLKVjOAFFCDx9PbX5ycJWDoWMcwdJH9RhkPu1dOgn5TrxLot/Gx6lWFuAUNQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-arrow-functions/7.18.6_@babel+core@7.20.2:
    resolution: {integrity: sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-block-scoping/7.20.2_@babel+core@7.20.2:
    resolution: {integrity: sha512-y5V15+04ry69OV2wULmwhEA6jwSWXO1TwAtIwiPXcvHcoOQUqpyMVd2bDsQJMW8AurjulIyUV8kDqtjSwHy1uQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-computed-properties/7.18.9_@babel+core@7.20.2:
    resolution: {integrity: sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-destructuring/7.20.2_@babel+core@7.20.2:
    resolution: {integrity: sha512-mENM+ZHrvEgxLTBXUiQ621rRXZes3KWUv6NdQlrnr1TkWVw+hUjQBZuP2X32qKlrlG2BzgR95gkuCRSkJl8vIw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-parameters/7.20.3_@babel+core@7.20.2:
    resolution: {integrity: sha512-oZg/Fpx0YDrj13KsLyO8I/CX3Zdw7z0O9qOd95SqcoIzuqy/WTGWvePeHAnZCN54SfdyjHcb1S30gc8zlzlHcA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
    dev: true

  /@babel/plugin-transform-spread/7.19.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/helper-skip-transparent-expression-wrappers': 7.20.0
    dev: true

  /@babel/plugin-transform-typescript/7.20.2_@babel+core@7.20.2:
    resolution: {integrity: sha512-jvS+ngBfrnTUBfOQq8NfGnSbF9BrqlR6hjJ2yVxMkmO5nL/cdifNbI30EfjRlN4g5wYWNnMPyj5Sa6R1pbLeag==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-create-class-features-plugin': 7.20.2_@babel+core@7.20.2
      '@babel/helper-plugin-utils': 7.20.2
      '@babel/plugin-syntax-typescript': 7.20.0_@babel+core@7.20.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/template/7.18.10:
    resolution: {integrity: sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/parser': 7.20.3
      '@babel/types': 7.20.2
    dev: true

  /@babel/traverse/7.20.1:
    resolution: {integrity: sha512-d3tN8fkVJwFLkHkBN479SOsw4DMZnz8cdbL/gvuDuzy3TS6Nfw80HuQqhw1pITbIruHyh7d1fMA47kWzmcUEGA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.18.6
      '@babel/generator': 7.20.4
      '@babel/helper-environment-visitor': 7.18.9
      '@babel/helper-function-name': 7.19.0
      '@babel/helper-hoist-variables': 7.18.6
      '@babel/helper-split-export-declaration': 7.18.6
      '@babel/parser': 7.20.3
      '@babel/types': 7.20.2
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/types/7.20.2:
    resolution: {integrity: sha512-FnnvsNWgZCr232sqtXggapvlkk/tuwR/qhGzcmxI0GXLCjmPYQPzio2FbdlWuY6y1sHFfQKk+rRbUZ9VStQMog==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.19.4
      '@babel/helper-validator-identifier': 7.19.1
      to-fast-properties: 2.0.0
    dev: true

  /@rollup/pluginutils/4.2.1:
    resolution: {integrity: sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==}
    engines: {node: '>= 8.0.0'}
    dependencies:
      estree-walker: registry.npmmirror.com/estree-walker/2.0.2
      picomatch: registry.npmmirror.com/picomatch/2.3.1
    dev: true

  /@vue/babel-helper-vue-jsx-merge-props/1.4.0:
    resolution: {integrity: sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==}
    dev: true

  /@vue/babel-plugin-transform-vue-jsx/1.4.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/helper-module-imports': 7.18.6
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.20.2
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      html-tags: 2.0.0
      lodash.kebabcase: 4.1.1
      svg-tags: 1.0.0
    dev: true

  /@vue/babel-preset-jsx/1.4.0_gk2qxvkpzxs3egijkbg2ff6vde:
    resolution: {integrity: sha512-QmfRpssBOPZWL5xw7fOuHNifCQcNQC1PrOo/4fu6xlhlKJJKSA3HqX92Nvgyx8fqHZTUGMPHmFA+IDqwXlqkSA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
      vue: '*'
    peerDependenciesMeta:
      vue:
        optional: true
    dependencies:
      '@babel/core': 7.20.2
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.20.2
      '@vue/babel-sugar-composition-api-inject-h': 1.4.0_@babel+core@7.20.2
      '@vue/babel-sugar-composition-api-render-instance': 1.4.0_@babel+core@7.20.2
      '@vue/babel-sugar-functional-vue': 1.4.0_@babel+core@7.20.2
      '@vue/babel-sugar-inject-h': 1.4.0_@babel+core@7.20.2
      '@vue/babel-sugar-v-model': 1.4.0_@babel+core@7.20.2
      '@vue/babel-sugar-v-on': 1.4.0_@babel+core@7.20.2
      vue: 2.6.11
    dev: true

  /@vue/babel-sugar-composition-api-inject-h/1.4.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.20.2
    dev: true

  /@vue/babel-sugar-composition-api-render-instance/1.4.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.20.2
    dev: true

  /@vue/babel-sugar-functional-vue/1.4.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.20.2
    dev: true

  /@vue/babel-sugar-inject-h/1.4.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.20.2
    dev: true

  /@vue/babel-sugar-v-model/1.4.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.20.2
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.20.2
      camelcase: 5.3.1
      html-tags: 2.0.0
      svg-tags: 1.0.0
    dev: true

  /@vue/babel-sugar-v-on/1.4.0_@babel+core@7.20.2:
    resolution: {integrity: sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-syntax-jsx': 7.18.6_@babel+core@7.20.2
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0_@babel+core@7.20.2
      camelcase: 5.3.1
    dev: true

  /@vue/component-compiler-utils/3.3.0:
    resolution: {integrity: sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==}
    dependencies:
      consolidate: 0.15.1
      hash-sum: 1.0.2
      lru-cache: 4.1.5
      merge-source-map: 1.1.0
      postcss: 7.0.39
      postcss-selector-parser: 6.0.10
      source-map: 0.6.1
      vue-template-es2015-compiler: 1.9.1
    optionalDependencies:
      prettier: registry.npmmirror.com/prettier/2.7.1
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers
    dev: true

  /ansi-styles/3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /anymatch/3.1.2:
    resolution: {integrity: sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: registry.npmmirror.com/picomatch/2.3.1
    dev: true

  /async-validator/1.8.5:
    resolution: {integrity: sha512-tXBM+1m056MAX0E8TL2iCjg8WvSyXu0Zc8LNtYqrVeyoL3+esHRZ4SieE9fKQyyU09uONjnMEjrNBMqT0mbvmA==}
    dependencies:
      babel-runtime: 6.26.0
    dev: true

  /babel-helper-vue-jsx-merge-props/2.0.3:
    resolution: {integrity: sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg==}
    dev: true

  /babel-runtime/6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: true

  /binary-extensions/2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}
    dev: true

  /bluebird/3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}
    dev: true

  /braces/3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1
    dev: true

  /browserslist/4.21.4:
    resolution: {integrity: sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001431
      electron-to-chromium: 1.4.284
      node-releases: 2.0.6
      update-browserslist-db: 1.0.10_browserslist@4.21.4
    dev: true

  /camelcase/5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}
    dev: true

  /caniuse-lite/1.0.30001431:
    resolution: {integrity: sha512-zBUoFU0ZcxpvSt9IU66dXVT/3ctO1cy4y9cscs1szkPlcWb6pasYM144GqrUygUbT+k7cmUCW61cvskjcv0enQ==}
    dev: true

  /chokidar/3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.2
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents/2.3.2
    dev: true

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: true

  /color-name/1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}
    dev: true

  /consolidate/0.15.1:
    resolution: {integrity: sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==}
    engines: {node: '>= 0.10.0'}
    peerDependencies:
      arc-templates: ^0.5.3
      atpl: '>=0.7.6'
      babel-core: ^6.26.3
      bracket-template: ^1.1.5
      coffee-script: ^1.12.7
      dot: ^1.1.3
      dust: ^0.3.0
      dustjs-helpers: ^1.7.4
      dustjs-linkedin: ^2.7.5
      eco: ^1.1.0-rc-3
      ect: ^0.5.9
      ejs: ^3.1.5
      haml-coffee: ^1.14.1
      hamlet: ^0.3.3
      hamljs: ^0.6.2
      handlebars: ^4.7.6
      hogan.js: ^3.0.2
      htmling: ^0.0.8
      jade: ^1.11.0
      jazz: ^0.0.18
      jqtpl: ~1.1.0
      just: ^0.1.8
      liquid-node: ^3.0.1
      liquor: ^0.0.5
      lodash: ^4.17.20
      marko: ^3.14.4
      mote: ^0.2.0
      mustache: ^3.0.0
      nunjucks: ^3.2.2
      plates: ~0.4.11
      pug: ^3.0.0
      qejs: ^3.0.5
      ractive: ^1.3.12
      razor-tmpl: ^1.3.1
      react: ^16.13.1
      react-dom: ^16.13.1
      slm: ^2.0.0
      squirrelly: ^5.1.0
      swig: ^1.4.2
      swig-templates: ^2.0.3
      teacup: ^2.0.0
      templayed: '>=0.2.3'
      then-jade: '*'
      then-pug: '*'
      tinyliquid: ^0.2.34
      toffee: ^0.3.6
      twig: ^1.15.2
      twing: ^5.0.2
      underscore: ^1.11.0
      vash: ^0.13.0
      velocityjs: ^2.0.1
      walrus: ^0.10.1
      whiskers: ^0.4.0
    peerDependenciesMeta:
      arc-templates:
        optional: true
      atpl:
        optional: true
      babel-core:
        optional: true
      bracket-template:
        optional: true
      coffee-script:
        optional: true
      dot:
        optional: true
      dust:
        optional: true
      dustjs-helpers:
        optional: true
      dustjs-linkedin:
        optional: true
      eco:
        optional: true
      ect:
        optional: true
      ejs:
        optional: true
      haml-coffee:
        optional: true
      hamlet:
        optional: true
      hamljs:
        optional: true
      handlebars:
        optional: true
      hogan.js:
        optional: true
      htmling:
        optional: true
      jade:
        optional: true
      jazz:
        optional: true
      jqtpl:
        optional: true
      just:
        optional: true
      liquid-node:
        optional: true
      liquor:
        optional: true
      lodash:
        optional: true
      marko:
        optional: true
      mote:
        optional: true
      mustache:
        optional: true
      nunjucks:
        optional: true
      plates:
        optional: true
      pug:
        optional: true
      qejs:
        optional: true
      ractive:
        optional: true
      razor-tmpl:
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      slm:
        optional: true
      squirrelly:
        optional: true
      swig:
        optional: true
      swig-templates:
        optional: true
      teacup:
        optional: true
      templayed:
        optional: true
      then-jade:
        optional: true
      then-pug:
        optional: true
      tinyliquid:
        optional: true
      toffee:
        optional: true
      twig:
        optional: true
      twing:
        optional: true
      underscore:
        optional: true
      vash:
        optional: true
      velocityjs:
        optional: true
      walrus:
        optional: true
      whiskers:
        optional: true
    dependencies:
      bluebird: 3.7.2
    dev: true

  /consolidate/0.16.0:
    resolution: {integrity: sha512-Nhl1wzCslqXYTJVDyJCu3ODohy9OfBMB5uD2BiBTzd7w+QY0lBzafkR8y8755yMYHAaMD4NuzbAw03/xzfw+eQ==}
    engines: {node: '>= 0.10.0'}
    peerDependencies:
      arc-templates: ^0.5.3
      atpl: '>=0.7.6'
      babel-core: ^6.26.3
      bracket-template: ^1.1.5
      coffee-script: ^1.12.7
      dot: ^1.1.3
      dust: ^0.3.0
      dustjs-helpers: ^1.7.4
      dustjs-linkedin: ^2.7.5
      eco: ^1.1.0-rc-3
      ect: ^0.5.9
      ejs: ^3.1.5
      haml-coffee: ^1.14.1
      hamlet: ^0.3.3
      hamljs: ^0.6.2
      handlebars: ^4.7.6
      hogan.js: ^3.0.2
      htmling: ^0.0.8
      jade: ^1.11.0
      jazz: ^0.0.18
      jqtpl: ~1.1.0
      just: ^0.1.8
      liquid-node: ^3.0.1
      liquor: ^0.0.5
      lodash: ^4.17.20
      marko: ^3.14.4
      mote: ^0.2.0
      mustache: ^4.0.1
      nunjucks: ^3.2.2
      plates: ~0.4.11
      pug: ^3.0.0
      qejs: ^3.0.5
      ractive: ^1.3.12
      razor-tmpl: ^1.3.1
      react: ^16.13.1
      react-dom: ^16.13.1
      slm: ^2.0.0
      squirrelly: ^5.1.0
      swig: ^1.4.2
      swig-templates: ^2.0.3
      teacup: ^2.0.0
      templayed: '>=0.2.3'
      then-jade: '*'
      then-pug: '*'
      tinyliquid: ^0.2.34
      toffee: ^0.3.6
      twig: ^1.15.2
      twing: ^5.0.2
      underscore: ^1.11.0
      vash: ^0.13.0
      velocityjs: ^2.0.1
      walrus: ^0.10.1
      whiskers: ^0.4.0
    peerDependenciesMeta:
      arc-templates:
        optional: true
      atpl:
        optional: true
      babel-core:
        optional: true
      bracket-template:
        optional: true
      coffee-script:
        optional: true
      dot:
        optional: true
      dust:
        optional: true
      dustjs-helpers:
        optional: true
      dustjs-linkedin:
        optional: true
      eco:
        optional: true
      ect:
        optional: true
      ejs:
        optional: true
      haml-coffee:
        optional: true
      hamlet:
        optional: true
      hamljs:
        optional: true
      handlebars:
        optional: true
      hogan.js:
        optional: true
      htmling:
        optional: true
      jade:
        optional: true
      jazz:
        optional: true
      jqtpl:
        optional: true
      just:
        optional: true
      liquid-node:
        optional: true
      liquor:
        optional: true
      lodash:
        optional: true
      marko:
        optional: true
      mote:
        optional: true
      mustache:
        optional: true
      nunjucks:
        optional: true
      plates:
        optional: true
      pug:
        optional: true
      qejs:
        optional: true
      ractive:
        optional: true
      razor-tmpl:
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      slm:
        optional: true
      squirrelly:
        optional: true
      swig:
        optional: true
      swig-templates:
        optional: true
      teacup:
        optional: true
      templayed:
        optional: true
      then-jade:
        optional: true
      then-pug:
        optional: true
      tinyliquid:
        optional: true
      toffee:
        optional: true
      twig:
        optional: true
      twing:
        optional: true
      underscore:
        optional: true
      vash:
        optional: true
      velocityjs:
        optional: true
      walrus:
        optional: true
      whiskers:
        optional: true
    dependencies:
      bluebird: 3.7.2
    dev: true

  /convert-source-map/1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}
    dev: true

  /core-js/2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true
    dev: true

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /de-indent/1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}
    dev: true

  /debug/4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /deepmerge/1.5.2:
    resolution: {integrity: sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /deepmerge/4.2.2:
    resolution: {integrity: sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /electron-to-chromium/1.4.284:
    resolution: {integrity: sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==}
    dev: true

  /element-ui/2.15.7_vue@2.6.11:
    resolution: {integrity: sha512-+J6rnXajxzLwV6w8Q6bf7Yqzk1FO1ewbIrCy/4B5alnd7tj8WEpfQoAvISirVaUGVGy77d9Ji3o2bF4f0AsJLQ==}
    peerDependencies:
      vue: ^2.5.17
    dependencies:
      async-validator: 1.8.5
      babel-helper-vue-jsx-merge-props: 2.0.3
      deepmerge: 1.5.2
      normalize-wheel: 1.0.1
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 1.1.0
      vue: 2.6.11
    dev: true

  /esbuild/0.15.13:
    resolution: {integrity: sha512-Cu3SC84oyzzhrK/YyN4iEVy2jZu5t2fz66HEOShHURcjSkOSAVL8C/gfUT+lDJxkVHpg8GZ10DD0rMHRPqMFaQ==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': registry.npmmirror.com/@esbuild/android-arm/0.15.13
      '@esbuild/linux-loong64': registry.npmmirror.com/@esbuild/linux-loong64/0.15.13
      esbuild-android-64: registry.npmmirror.com/esbuild-android-64/0.15.13
      esbuild-android-arm64: registry.npmmirror.com/esbuild-android-arm64/0.15.13
      esbuild-darwin-64: registry.npmmirror.com/esbuild-darwin-64/0.15.13
      esbuild-darwin-arm64: registry.npmmirror.com/esbuild-darwin-arm64/0.15.13
      esbuild-freebsd-64: registry.npmmirror.com/esbuild-freebsd-64/0.15.13
      esbuild-freebsd-arm64: registry.npmmirror.com/esbuild-freebsd-arm64/0.15.13
      esbuild-linux-32: registry.npmmirror.com/esbuild-linux-32/0.15.13
      esbuild-linux-64: registry.npmmirror.com/esbuild-linux-64/0.15.13
      esbuild-linux-arm: registry.npmmirror.com/esbuild-linux-arm/0.15.13
      esbuild-linux-arm64: registry.npmmirror.com/esbuild-linux-arm64/0.15.13
      esbuild-linux-mips64le: registry.npmmirror.com/esbuild-linux-mips64le/0.15.13
      esbuild-linux-ppc64le: registry.npmmirror.com/esbuild-linux-ppc64le/0.15.13
      esbuild-linux-riscv64: registry.npmmirror.com/esbuild-linux-riscv64/0.15.13
      esbuild-linux-s390x: registry.npmmirror.com/esbuild-linux-s390x/0.15.13
      esbuild-netbsd-64: registry.npmmirror.com/esbuild-netbsd-64/0.15.13
      esbuild-openbsd-64: registry.npmmirror.com/esbuild-openbsd-64/0.15.13
      esbuild-sunos-64: registry.npmmirror.com/esbuild-sunos-64/0.15.13
      esbuild-windows-32: registry.npmmirror.com/esbuild-windows-32/0.15.13
      esbuild-windows-64: registry.npmmirror.com/esbuild-windows-64/0.15.13
      esbuild-windows-arm64: registry.npmmirror.com/esbuild-windows-arm64/0.15.13
    dev: true

  /escalade/3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}
    dev: true

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}
    dev: true

  /fill-range/7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /fs-extra/10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: 4.2.10
      jsonfile: 6.1.0
      universalify: 2.0.0
    dev: true

  /function-bind/1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}
    dev: true

  /gensync/1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}
    dev: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /globals/11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}
    dev: true

  /graceful-fs/4.2.10:
    resolution: {integrity: sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==}
    dev: true

  /has-flag/3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}
    dev: true

  /has/1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1
    dev: true

  /hash-sum/1.0.2:
    resolution: {integrity: sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==}
    dev: true

  /hash-sum/2.0.0:
    resolution: {integrity: sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==}
    dev: true

  /he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: true

  /html-tags/2.0.0:
    resolution: {integrity: sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g==}
    engines: {node: '>=4'}
    dev: true

  /immutable/4.1.0:
    resolution: {integrity: sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==}
    dev: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0
    dev: true

  /is-core-module/2.11.0:
    resolution: {integrity: sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==}
    dependencies:
      has: 1.0.3
    dev: true

  /is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}
    dev: true

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: true

  /jsesc/2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /json5/2.2.1:
    resolution: {integrity: sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jsonfile/6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.0
    optionalDependencies:
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.10
    dev: true

  /lodash.kebabcase/4.1.1:
    resolution: {integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==}
    dev: true

  /lru-cache/4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2
    dev: true

  /magic-string/0.26.7:
    resolution: {integrity: sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==}
    engines: {node: '>=12'}
    dependencies:
      sourcemap-codec: registry.npmmirror.com/sourcemap-codec/1.4.8
    dev: true

  /merge-source-map/1.1.0:
    resolution: {integrity: sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==}
    dependencies:
      source-map: 0.6.1
    dev: true

  /ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: true

  /nanoid/3.3.4:
    resolution: {integrity: sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /node-releases/2.0.6:
    resolution: {integrity: sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==}
    dev: true

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /normalize-wheel/1.0.1:
    resolution: {integrity: sha512-1OnlAPZ3zgrk8B91HyRj+eVv+kS5u+Z0SCsak6Xil/kmgEia50ga7zfkumayonZrImffAxPU/5WcyGhzetHNPA==}
    dev: true

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: true

  /picocolors/0.2.1:
    resolution: {integrity: sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==}
    dev: true

  /picocolors/1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}
    dev: true

  /postcss-selector-parser/6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss/7.0.39:
    resolution: {integrity: sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1
    dev: true

  /postcss/8.4.19:
    resolution: {integrity: sha512-h+pbPsyhlYj6N2ozBmHhHrs9DzGmbaarbLvWipMRO7RLS+v4onj26MPFXA5OBYFxyqYhUJK456SwDcY9H2/zsA==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.4
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: true

  /prettier/2.7.1:
    resolution: {integrity: sha512-ujppO+MkdPqoVINuDFDRLClm7D78qbDt0/NR+wp5FqEZOoTNAjPHWj17QRhu7geIHJfcNhRk1XVQmF8Bp3ye+g==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dev: true

  /pseudomap/1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==}
    dev: true

  /querystring/0.2.1:
    resolution: {integrity: sha512-wkvS7mL/JMugcup3/rMitHmd9ecIGd2lhFhK9N3UUQ450h66d1r3Y9nvXzQAW1Lq+wyx61k/1pfKS5KuKiyEbg==}
    engines: {node: '>=0.4.x'}
    deprecated: The querystring API is considered Legacy. new code should use the URLSearchParams API instead.
    dev: true

  /readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: registry.npmmirror.com/picomatch/2.3.1
    dev: true

  /regenerator-runtime/0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: true

  /resize-observer-polyfill/1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: true

  /resolve/1.22.1:
    resolution: {integrity: sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==}
    hasBin: true
    dependencies:
      is-core-module: 2.11.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /rollup/2.79.1:
    resolution: {integrity: sha512-uKxbd0IhMZOhjAiD5oAFp7BqvkA4Dv47qpOCtaNvng4HBwdbWtdOh8f5nZNuk2rp51PMGk3bzfWu5oayNEuYnw==}
    engines: {node: '>=10.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents/2.3.2
    dev: true

  /sass/1.56.1:
    resolution: {integrity: sha512-VpEyKpyBPCxE7qGDtOcdJ6fFbcpOM+Emu7uZLxVrkX8KVU/Dp5UF7WLvzqRuUhB6mqqQt1xffLoG+AndxTZrCQ==}
    engines: {node: '>=12.0.0'}
    hasBin: true
    dependencies:
      chokidar: 3.5.3
      immutable: 4.1.0
      source-map-js: 1.0.2
    dev: true

  /semver/6.3.0:
    resolution: {integrity: sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==}
    hasBin: true
    dev: true

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /source-map-js/1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map/0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}
    dev: true

  /supports-color/5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: true

  /svg-tags/1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==}
    dev: true

  /throttle-debounce/1.1.0:
    resolution: {integrity: sha512-XH8UiPCQcWNuk2LYePibW/4qL97+ZQ1AN3FNXwZRBNPPowo/NRU5fAlDCSNBJIYCKbioZfuYtMhG4quqoJhVzg==}
    engines: {node: '>=4'}
    dev: true

  /to-fast-properties/2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}
    dev: true

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /universalify/2.0.0:
    resolution: {integrity: sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /update-browserslist-db/1.0.10_browserslist@4.21.4:
    resolution: {integrity: sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.21.4
      escalade: 3.1.1
      picocolors: 1.0.0
    dev: true

  /util-deprecate/1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}
    dev: true

  /vite-plugin-vue2/2.0.2_lauxldphyul6wmyri5znbggen4:
    resolution: {integrity: sha512-Oo1iwc5Zo376s3MYXqS7j+KXs26EjiyWV8/dmI23SoorO3zaAgnBefR45Zme+QtM407tJ2MVq0mqfI10qA5+LQ==}
    peerDependencies:
      vite: ^2.0.0 || ^3.0.0
      vue-template-compiler: ^2.2.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/parser': 7.20.3
      '@babel/plugin-proposal-class-properties': 7.18.6_@babel+core@7.20.2
      '@babel/plugin-proposal-decorators': 7.20.2_@babel+core@7.20.2
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6_@babel+core@7.20.2
      '@babel/plugin-proposal-object-rest-spread': 7.20.2_@babel+core@7.20.2
      '@babel/plugin-proposal-optional-chaining': 7.18.9_@babel+core@7.20.2
      '@babel/plugin-transform-arrow-functions': 7.18.6_@babel+core@7.20.2
      '@babel/plugin-transform-block-scoping': 7.20.2_@babel+core@7.20.2
      '@babel/plugin-transform-computed-properties': 7.18.9_@babel+core@7.20.2
      '@babel/plugin-transform-destructuring': 7.20.2_@babel+core@7.20.2
      '@babel/plugin-transform-parameters': 7.20.3_@babel+core@7.20.2
      '@babel/plugin-transform-spread': 7.19.0_@babel+core@7.20.2
      '@babel/plugin-transform-typescript': 7.20.2_@babel+core@7.20.2
      '@rollup/pluginutils': 4.2.1
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-preset-jsx': 1.4.0_gk2qxvkpzxs3egijkbg2ff6vde
      '@vue/component-compiler-utils': 3.3.0
      consolidate: 0.16.0
      debug: 4.3.4
      fs-extra: 10.1.0
      hash-sum: 2.0.0
      magic-string: 0.26.7
      prettier: 2.7.1
      querystring: 0.2.1
      rollup: 2.79.1
      slash: 3.0.0
      source-map: 0.7.4
      vite: 3.2.3_sass@1.56.1
      vue-template-babel-compiler: 1.2.0_i6zkvopayoohexkqkaqc4apoem
      vue-template-compiler: 2.6.11
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - supports-color
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - vue
      - walrus
      - whiskers
    dev: true

  /vite/3.2.3_sass@1.56.1:
    resolution: {integrity: sha512-h8jl1TZ76eGs3o2dIBSsvXDLb1m/Ec1iej8ZMdz+PsaFUsftZeWe2CZOI3qogEsMNaywc17gu0q6cQDzh/weCQ==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      esbuild: 0.15.13
      postcss: 8.4.19
      resolve: 1.22.1
      rollup: 2.79.1
      sass: 1.56.1
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents/2.3.2
    dev: true

  /vue-router/3.5.3_vue@2.6.11:
    resolution: {integrity: sha512-FUlILrW3DGitS2h+Xaw8aRNvGTwtuaxrRkNSHWTizOfLUie7wuYwezeZ50iflRn8YPV5kxmU2LQuu3nM/b3Zsg==}
    peerDependencies:
      vue: ^2
    dependencies:
      vue: 2.6.11
    dev: true

  /vue-template-babel-compiler/1.2.0_i6zkvopayoohexkqkaqc4apoem:
    resolution: {integrity: sha512-CScBSX1/wCdmmZ/Lvj/63p2CCVTS0FMj0F69VRBo73CuJrjvPAPGmeNJ7D/cwt/VS2PduowRWbO8N4Zh4Z3b0g==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      vue-template-compiler: ^2.6.0
    dependencies:
      '@babel/core': 7.20.2
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6_@babel+core@7.20.2
      '@babel/plugin-proposal-object-rest-spread': 7.20.2_@babel+core@7.20.2
      '@babel/plugin-proposal-optional-chaining': 7.18.9_@babel+core@7.20.2
      '@babel/plugin-transform-arrow-functions': 7.18.6_@babel+core@7.20.2
      '@babel/plugin-transform-block-scoping': 7.20.2_@babel+core@7.20.2
      '@babel/plugin-transform-computed-properties': 7.18.9_@babel+core@7.20.2
      '@babel/plugin-transform-destructuring': 7.20.2_@babel+core@7.20.2
      '@babel/plugin-transform-parameters': 7.20.3_@babel+core@7.20.2
      '@babel/plugin-transform-spread': 7.19.0_@babel+core@7.20.2
      '@babel/types': 7.20.2
      deepmerge: 4.2.2
      vue-template-compiler: 2.6.11
    transitivePeerDependencies:
      - supports-color
    dev: true

  /vue-template-compiler/2.6.11:
    resolution: {integrity: sha512-KIq15bvQDrcCjpGjrAhx4mUlyyHfdmTaoNfeoATHLAiWB+MU3cx4lOzMwrnUh9cCxy0Lt1T11hAFY6TQgroUAA==}
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0
    dev: true

  /vue-template-es2015-compiler/1.9.1:
    resolution: {integrity: sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==}
    dev: true

  /vue/2.6.11:
    resolution: {integrity: sha512-VfPwgcGABbGAue9+sfrD4PuwFar7gPb1yl1UK1MwXoQPAw0BKSqWfoYCT/ThFrdEVWoI51dBuyCoiNU9bZDZxQ==}
    dev: true

  /yallist/2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==}
    dev: true

  registry.npmmirror.com/@ampproject/remapping/2.2.0:
    resolution: {integrity: sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.2.0.tgz}
    name: '@ampproject/remapping'
    version: 2.2.0
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': registry.npmmirror.com/@jridgewell/gen-mapping/0.1.1
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping/0.3.17
    dev: true

  registry.npmmirror.com/@antfu/install-pkg/0.1.1:
    resolution: {integrity: sha512-LyB/8+bSfa0DFGC06zpCEfs89/XoWZwws5ygEa5D+Xsm3OfI+aXQ86VgVG7Acyef+rSZ5HE7J8rrxzrQeM3PjQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@antfu/install-pkg/-/install-pkg-0.1.1.tgz}
    name: '@antfu/install-pkg'
    version: 0.1.1
    dependencies:
      execa: registry.npmmirror.com/execa/5.1.1
      find-up: registry.npmmirror.com/find-up/5.0.0
    dev: true

  registry.npmmirror.com/@antfu/utils/0.5.2:
    resolution: {integrity: sha512-CQkeV+oJxUazwjlHD0/3ZD08QWKuGQkhnrKo3e6ly5pd48VUpXbb77q0xMU4+vc2CkJnDS02Eq/M9ugyX20XZA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@antfu/utils/-/utils-0.5.2.tgz}
    name: '@antfu/utils'
    version: 0.5.2
    dev: true

  registry.npmmirror.com/@esbuild/android-arm/0.15.13:
    resolution: {integrity: sha512-RY2fVI8O0iFUNvZirXaQ1vMvK0xhCcl0gqRj74Z6yEiO1zAUa7hbsdwZM1kzqbxHK7LFyMizipfXT3JME+12Hw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.15.13.tgz}
    name: '@esbuild/android-arm'
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@esbuild/linux-loong64/0.15.13:
    resolution: {integrity: sha512-+BoyIm4I8uJmH/QDIH0fu7MG0AEx9OXEDXnqptXCwKOlOqZiS4iraH1Nr7/ObLMokW3sOCeBNyD68ATcV9b9Ag==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.15.13.tgz}
    name: '@esbuild/linux-loong64'
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/@iconify-json/ep/1.1.8:
    resolution: {integrity: sha512-pHCrsWU1R9/pTDU+Fps4+mjqOQFLtpGdXWegkhQ1P1DlgQAlCPyICtl6E1s8b7VwJMeZXaK84HA02UF6WD0o/Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@iconify-json/ep/-/ep-1.1.8.tgz}
    name: '@iconify-json/ep'
    version: 1.1.8
    dependencies:
      '@iconify/types': registry.npmmirror.com/@iconify/types/2.0.0
    dev: true

  registry.npmmirror.com/@iconify-json/fa6-solid/1.1.7:
    resolution: {integrity: sha512-nFLZmISKqutQhu+XWy/s80yAZlrMBlrTJW4VNNgY/ZL1JoH6nFxff6+12740/n8AeA++5U33S+/CmK8SB8RJhw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@iconify-json/fa6-solid/-/fa6-solid-1.1.7.tgz}
    name: '@iconify-json/fa6-solid'
    version: 1.1.7
    dependencies:
      '@iconify/types': registry.npmmirror.com/@iconify/types/2.0.0
    dev: true

  registry.npmmirror.com/@iconify/types/2.0.0:
    resolution: {integrity: sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@iconify/types/-/types-2.0.0.tgz}
    name: '@iconify/types'
    version: 2.0.0
    dev: true

  registry.npmmirror.com/@iconify/utils/2.0.1:
    resolution: {integrity: sha512-t8IyICk25wgZL4YKn/2kYfjG5MGA6EWZlaUJZ1OEIku4V+kX9V900T5E4HIqS3hLyD6/RJET0zY4vxO9pHLHHw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@iconify/utils/-/utils-2.0.1.tgz}
    name: '@iconify/utils'
    version: 2.0.1
    dependencies:
      '@antfu/install-pkg': registry.npmmirror.com/@antfu/install-pkg/0.1.1
      '@antfu/utils': registry.npmmirror.com/@antfu/utils/0.5.2
      '@iconify/types': registry.npmmirror.com/@iconify/types/2.0.0
      debug: registry.npmmirror.com/debug/4.3.4
      kolorist: registry.npmmirror.com/kolorist/1.6.0
      local-pkg: registry.npmmirror.com/local-pkg/0.4.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@jridgewell/gen-mapping/0.1.1:
    resolution: {integrity: sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz}
    name: '@jridgewell/gen-mapping'
    version: 0.1.1
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': registry.npmmirror.com/@jridgewell/set-array/1.1.2
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec/1.4.14
    dev: true

  registry.npmmirror.com/@jridgewell/gen-mapping/0.3.2:
    resolution: {integrity: sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz}
    name: '@jridgewell/gen-mapping'
    version: 0.3.2
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': registry.npmmirror.com/@jridgewell/set-array/1.1.2
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec/1.4.14
      '@jridgewell/trace-mapping': registry.npmmirror.com/@jridgewell/trace-mapping/0.3.17
    dev: true

  registry.npmmirror.com/@jridgewell/resolve-uri/3.1.0:
    resolution: {integrity: sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz}
    name: '@jridgewell/resolve-uri'
    version: 3.1.0
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmmirror.com/@jridgewell/set-array/1.1.2:
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.1.2.tgz}
    name: '@jridgewell/set-array'
    version: 1.1.2
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmmirror.com/@jridgewell/sourcemap-codec/1.4.14:
    resolution: {integrity: sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz}
    name: '@jridgewell/sourcemap-codec'
    version: 1.4.14
    dev: true

  registry.npmmirror.com/@jridgewell/trace-mapping/0.3.17:
    resolution: {integrity: sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz}
    name: '@jridgewell/trace-mapping'
    version: 0.3.17
    dependencies:
      '@jridgewell/resolve-uri': registry.npmmirror.com/@jridgewell/resolve-uri/3.1.0
      '@jridgewell/sourcemap-codec': registry.npmmirror.com/@jridgewell/sourcemap-codec/1.4.14
    dev: true

  registry.npmmirror.com/@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz}
    name: '@nodelib/fs.scandir'
    version: 2.1.5
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': registry.npmmirror.com/@nodelib/fs.stat/2.0.5
      run-parallel: registry.npmmirror.com/run-parallel/1.2.0
    dev: true

  registry.npmmirror.com/@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz}
    name: '@nodelib/fs.stat'
    version: 2.0.5
    engines: {node: '>= 8'}
    dev: true

  registry.npmmirror.com/@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz}
    name: '@nodelib/fs.walk'
    version: 1.2.8
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': registry.npmmirror.com/@nodelib/fs.scandir/2.1.5
      fastq: registry.npmmirror.com/fastq/1.13.0
    dev: true

  registry.npmmirror.com/@polka/url/1.0.0-next.21:
    resolution: {integrity: sha512-a5Sab1C4/icpTZVzZc5Ghpz88yQtGOyNqYXcZgOssB2uuAr+wF/MvN6bgtW32q7HHrvBki+BsZ0OuNv6EV3K9g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@polka/url/-/url-1.0.0-next.21.tgz}
    name: '@polka/url'
    version: 1.0.0-next.21
    dev: true

  registry.npmmirror.com/@rollup/pluginutils/5.0.2:
    resolution: {integrity: sha512-pTd9rIsP92h+B6wWwFbW8RkZv4hiR/xKsqre4SIuAOaOEQRxi0lqLke9k2/7WegC85GgUs9pjmOjCUi3In4vwA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.0.2.tgz}
    name: '@rollup/pluginutils'
    version: 5.0.2
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0
    peerDependenciesMeta:
      rollup:
        optional: true
    dependencies:
      '@types/estree': registry.npmmirror.com/@types/estree/1.0.0
      estree-walker: registry.npmmirror.com/estree-walker/2.0.2
      picomatch: registry.npmmirror.com/picomatch/2.3.1
    dev: true

  registry.npmmirror.com/@types/estree/1.0.0:
    resolution: {integrity: sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/estree/-/estree-1.0.0.tgz}
    name: '@types/estree'
    version: 1.0.0
    dev: true

  registry.npmmirror.com/@types/fined/1.1.3:
    resolution: {integrity: sha512-CWYnSRnun3CGbt6taXeVo2lCbuaj4mchVJ4UF/BdU5TSuIn3AmS13pGMwCsBUoehGbhZrBrpNJZSZI5EVilXww==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/fined/-/fined-1.1.3.tgz}
    name: '@types/fined'
    version: 1.1.3
    dev: true

  registry.npmmirror.com/@types/inquirer/8.2.5:
    resolution: {integrity: sha512-QXlzybid60YtAwfgG3cpykptRYUx2KomzNutMlWsQC64J/WG/gQSl+P4w7A21sGN0VIxRVava4rgnT7FQmFCdg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/inquirer/-/inquirer-8.2.5.tgz}
    name: '@types/inquirer'
    version: 8.2.5
    dependencies:
      '@types/through': registry.npmmirror.com/@types/through/0.0.30
    dev: true

  registry.npmmirror.com/@types/liftoff/4.0.0:
    resolution: {integrity: sha512-Ny/PJkO6nxWAQnaet8q/oWz15lrfwvdvBpuY4treB0CSsBO1CG0fVuNLngR3m3bepQLd+E4c3Y3DlC2okpUvPw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/liftoff/-/liftoff-4.0.0.tgz}
    name: '@types/liftoff'
    version: 4.0.0
    dependencies:
      '@types/fined': registry.npmmirror.com/@types/fined/1.1.3
      '@types/node': registry.npmmirror.com/@types/node/18.11.9
    dev: true

  registry.npmmirror.com/@types/node/18.11.9:
    resolution: {integrity: sha512-CRpX21/kGdzjOpFsZSkcrXMGIBWMGNIHXXBVFSH+ggkftxg+XYP20TESbh+zFvFj3EQOl5byk0HTRn1IL6hbqg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/node/-/node-18.11.9.tgz}
    name: '@types/node'
    version: 18.11.9
    dev: true

  registry.npmmirror.com/@types/through/0.0.30:
    resolution: {integrity: sha512-FvnCJljyxhPM3gkRgWmxmDZyAQSiBQQWLI0A0VFL0K7W1oRUrPJSqNO0NvTnLkBcotdlp3lKvaT0JrnyRDkzOg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@types/through/-/through-0.0.30.tgz}
    name: '@types/through'
    version: 0.0.30
    dependencies:
      '@types/node': registry.npmmirror.com/@types/node/18.11.9
    dev: true

  registry.npmmirror.com/@unocss/astro/0.46.5_vite@3.2.3:
    resolution: {integrity: sha512-Ey4ReY0ult1IARdgYsEynwGbfvmwoUBOFKKdQzsm6TQR/HcSGLND4Yzoa0OTotG5gj6h9jCPjypZtw6xCUqyzg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/astro/-/astro-0.46.5.tgz}
    id: registry.npmmirror.com/@unocss/astro/0.46.5
    name: '@unocss/astro'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      '@unocss/reset': registry.npmmirror.com/@unocss/reset/0.46.5
      '@unocss/vite': registry.npmmirror.com/@unocss/vite/0.46.5_vite@3.2.3
    transitivePeerDependencies:
      - rollup
      - vite
    dev: true

  registry.npmmirror.com/@unocss/cli/0.46.5:
    resolution: {integrity: sha512-NOx/b/nsG1fGdfJ+gWhoVyvrfVVyJ1Pvb5VKsMx24HBoOvoiQd6BO84xQZ+zCdO0IYgw/F4THxaPsyHM3KL8wA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/cli/-/cli-0.46.5.tgz}
    name: '@unocss/cli'
    version: 0.46.5
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      '@ampproject/remapping': registry.npmmirror.com/@ampproject/remapping/2.2.0
      '@rollup/pluginutils': registry.npmmirror.com/@rollup/pluginutils/5.0.2
      '@unocss/config': registry.npmmirror.com/@unocss/config/0.46.5
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      '@unocss/preset-uno': registry.npmmirror.com/@unocss/preset-uno/0.46.5
      cac: registry.npmmirror.com/cac/6.7.14
      chokidar: registry.npmmirror.com/chokidar/3.5.3
      colorette: registry.npmmirror.com/colorette/2.0.19
      consola: registry.npmmirror.com/consola/2.15.3
      fast-glob: registry.npmmirror.com/fast-glob/3.2.12
      magic-string: registry.npmmirror.com/magic-string/0.26.7
      pathe: registry.npmmirror.com/pathe/0.3.9
      perfect-debounce: registry.npmmirror.com/perfect-debounce/0.1.3
    transitivePeerDependencies:
      - rollup
    dev: true

  registry.npmmirror.com/@unocss/config/0.46.5:
    resolution: {integrity: sha512-PCrT5GHyxaMroip8kLiX7vQDRYThugkAJNHHULWh/gWuvw5V/uMseeAisfMaRfNFXvtCRQeroNUggfAIXMNzxA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/config/-/config-0.46.5.tgz}
    name: '@unocss/config'
    version: 0.46.5
    engines: {node: '>=14'}
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      unconfig: registry.npmmirror.com/unconfig/0.3.7
    dev: true

  registry.npmmirror.com/@unocss/core/0.46.5:
    resolution: {integrity: sha512-apPWISH2ASKySyp4vBAkEzr5XNJDv4mu26eQ4wvg2a32cHRokUsiHLWFNMDe7oJquivTAOHsXqbLQuR9ePKNSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/core/-/core-0.46.5.tgz}
    name: '@unocss/core'
    version: 0.46.5
    dev: true

  registry.npmmirror.com/@unocss/inspector/0.46.5:
    resolution: {integrity: sha512-L7bMqtp94qtADeGm0ZU1IPtRatChSqHHxFG2s11+VuElPjktcnwnEfTPY5Obm++sy0kNNPU0M3xJD4TPk/M22g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/inspector/-/inspector-0.46.5.tgz}
    name: '@unocss/inspector'
    version: 0.46.5
    dependencies:
      gzip-size: registry.npmmirror.com/gzip-size/6.0.0
      sirv: registry.npmmirror.com/sirv/2.0.2
    dev: true

  registry.npmmirror.com/@unocss/preset-attributify/0.46.5:
    resolution: {integrity: sha512-phhhF0bgO+sFCiin9dstuOB5wxgTg3n3gE+uoCCfhZz2XI6GmmcL60cV5xTMhFv9kxedrvy8kS6UF+W1GAUang==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-attributify/-/preset-attributify-0.46.5.tgz}
    name: '@unocss/preset-attributify'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/preset-icons/0.46.5:
    resolution: {integrity: sha512-g1gMi++j233nc/5KcUti5+X+fQ9K36MzROx37pJCFz+jD5i7Y21hXOC/brP7k4tWo1IEFR8rsLgtvVEXOhu8Sw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-icons/-/preset-icons-0.46.5.tgz}
    name: '@unocss/preset-icons'
    version: 0.46.5
    dependencies:
      '@iconify/utils': registry.npmmirror.com/@iconify/utils/2.0.1
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      ohmyfetch: registry.npmmirror.com/ohmyfetch/0.4.21
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmmirror.com/@unocss/preset-mini/0.46.5:
    resolution: {integrity: sha512-pSiCUeNoLs9F5ux7BzsTKqQhTABvwtggv45Kza2wDIStPESLYHInPUHF57e753IhKdZSpX08gSRiYF4mXVcFWg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-mini/-/preset-mini-0.46.5.tgz}
    name: '@unocss/preset-mini'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/preset-tagify/0.46.5:
    resolution: {integrity: sha512-lE+nmm9Bf8ckr4pmVHzHYkLiePnfiC/HQTPHaa9CmRiBtUEljiMt/RVnRu/R7yuel+DDLdX7GfGg4oNstVB7Cg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-tagify/-/preset-tagify-0.46.5.tgz}
    name: '@unocss/preset-tagify'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/preset-typography/0.46.5:
    resolution: {integrity: sha512-wAf9XNNzJWrXEFWuEUXfY5Jmx7HPv5Mi+3EkOcPi8at7lcncWkF0PmLY/nI95nfXQZipkV+aDyHp3TsDn9FgAw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-typography/-/preset-typography-0.46.5.tgz}
    name: '@unocss/preset-typography'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/preset-uno/0.46.5:
    resolution: {integrity: sha512-O4f/cSdNAnK3MVXLgzihnOLrp12h3787U9bPZ1WS7Ow52D+kzBfRLVEWy4RWCGDN+ko/0q7Q7nqZOkrtcCELJQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-uno/-/preset-uno-0.46.5.tgz}
    name: '@unocss/preset-uno'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      '@unocss/preset-mini': registry.npmmirror.com/@unocss/preset-mini/0.46.5
      '@unocss/preset-wind': registry.npmmirror.com/@unocss/preset-wind/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/preset-web-fonts/0.46.5:
    resolution: {integrity: sha512-ITOtD9AeklSqsVzBlK6wKbABQnD1o9wubN4bGkz7cd0G0GEt7YkW9dYwmXPYqo9Stoga92MJPy/Z2mxKT0ejVw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-web-fonts/-/preset-web-fonts-0.46.5.tgz}
    name: '@unocss/preset-web-fonts'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      ohmyfetch: registry.npmmirror.com/ohmyfetch/0.4.21
    dev: true

  registry.npmmirror.com/@unocss/preset-wind/0.46.5:
    resolution: {integrity: sha512-v1n918arr7qg/AYjkvVCJDqWT7bd8UhdT7tBCcJcFOuPvJdToBoy3LcNxQXQpS7gcVGG7YiKE5rT4JwsV7U5XA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/preset-wind/-/preset-wind-0.46.5.tgz}
    name: '@unocss/preset-wind'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      '@unocss/preset-mini': registry.npmmirror.com/@unocss/preset-mini/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/reset/0.46.5:
    resolution: {integrity: sha512-DU1sisNixEaUsnfDdbU+POaedJLKUtalHnOOce2Txxrcakf7M2/I5/9cRIXt5diVbPjIyoDPcx+7Gn8K0cTGqg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/reset/-/reset-0.46.5.tgz}
    name: '@unocss/reset'
    version: 0.46.5
    dev: true

  registry.npmmirror.com/@unocss/scope/0.46.5:
    resolution: {integrity: sha512-gAA6a2zwal9GMW6HSp2dofUEzS6ZlLxg6/y8fSHHjJduKRZaq77IP3YdmyT4ZnPBIYsE7+u25lXeDOEFrEMSxg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/scope/-/scope-0.46.5.tgz}
    name: '@unocss/scope'
    version: 0.46.5
    dev: true

  registry.npmmirror.com/@unocss/transformer-attributify-jsx/0.46.5:
    resolution: {integrity: sha512-kQwGnV/yICU4LdjOBgKrn+m29Zb820W3HGqxI1MAyutbeWJObQbuMnSLhVCu6I9zjlvvFV9rfJ87z/Ggo1vnjQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/transformer-attributify-jsx/-/transformer-attributify-jsx-0.46.5.tgz}
    name: '@unocss/transformer-attributify-jsx'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/transformer-compile-class/0.46.5:
    resolution: {integrity: sha512-ntE+CJ2XzUyXKzT2MklM1GcVxLlJFpi6fYl0GqGqXMxvPdKHUaX9f4zASjuAUFr/od1MKhjHkujy8pyr6y2Q4g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/transformer-compile-class/-/transformer-compile-class-0.46.5.tgz}
    name: '@unocss/transformer-compile-class'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/transformer-directives/0.46.5:
    resolution: {integrity: sha512-jnskAINirtl0TW/8ELRR55AEQoy4yU1iBQuZcgJCfOGU6gkNsq6IRoBLcCiFoBmp8UdxvIuGo4k1SpnEf5x+gA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/transformer-directives/-/transformer-directives-0.46.5.tgz}
    name: '@unocss/transformer-directives'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      css-tree: registry.npmmirror.com/css-tree/2.2.1
    dev: true

  registry.npmmirror.com/@unocss/transformer-variant-group/0.46.5:
    resolution: {integrity: sha512-xBylFf8Q0z0jzr8OoqiIstC2ZXPeniENGANOruTzXlS+DJ2uuKNrwOuXpBbMTEA2D7lmLwtbyg1PZy3mDUB3iA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/transformer-variant-group/-/transformer-variant-group-0.46.5.tgz}
    name: '@unocss/transformer-variant-group'
    version: 0.46.5
    dependencies:
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
    dev: true

  registry.npmmirror.com/@unocss/vite/0.46.5_vite@3.2.3:
    resolution: {integrity: sha512-/auNcS1L3PjwAA3U/i9scJf2Zx3kkgCdKiXyfetjws4ddAnVE+LrDmIKbbdSUiWFoq9W2QOPpOPpV2xips2gmg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/@unocss/vite/-/vite-0.46.5.tgz}
    id: registry.npmmirror.com/@unocss/vite/0.46.5
    name: '@unocss/vite'
    version: 0.46.5
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0
    dependencies:
      '@ampproject/remapping': registry.npmmirror.com/@ampproject/remapping/2.2.0
      '@rollup/pluginutils': registry.npmmirror.com/@rollup/pluginutils/5.0.2
      '@unocss/config': registry.npmmirror.com/@unocss/config/0.46.5
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      '@unocss/inspector': registry.npmmirror.com/@unocss/inspector/0.46.5
      '@unocss/scope': registry.npmmirror.com/@unocss/scope/0.46.5
      '@unocss/transformer-directives': registry.npmmirror.com/@unocss/transformer-directives/0.46.5
      magic-string: registry.npmmirror.com/magic-string/0.26.7
      vite: 3.2.3_sass@1.56.1
    transitivePeerDependencies:
      - rollup
    dev: true

  registry.npmmirror.com/aggregate-error/3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/aggregate-error/-/aggregate-error-3.1.0.tgz}
    name: aggregate-error
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      clean-stack: registry.npmmirror.com/clean-stack/2.2.0
      indent-string: registry.npmmirror.com/indent-string/4.0.0
    dev: true

  registry.npmmirror.com/ansi-escapes/4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz}
    name: ansi-escapes
    version: 4.3.2
    engines: {node: '>=8'}
    dependencies:
      type-fest: registry.npmmirror.com/type-fest/0.21.3
    dev: true

  registry.npmmirror.com/ansi-regex/5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz}
    name: ansi-regex
    version: 5.0.1
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/ansi-regex/6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.0.1.tgz}
    name: ansi-regex
    version: 6.0.1
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz}
    name: ansi-styles
    version: 4.3.0
    engines: {node: '>=8'}
    dependencies:
      color-convert: registry.npmmirror.com/color-convert/2.0.1
    dev: true

  registry.npmmirror.com/anymatch/3.1.2:
    resolution: {integrity: sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/anymatch/-/anymatch-3.1.2.tgz}
    name: anymatch
    version: 3.1.2
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: registry.npmmirror.com/normalize-path/3.0.0
      picomatch: registry.npmmirror.com/picomatch/2.3.1
    dev: true

  registry.npmmirror.com/array-each/1.0.1:
    resolution: {integrity: sha512-zHjL5SZa68hkKHBFBK6DJCTtr9sfTCPCaph/L7tMSLcTFgy+zX7E+6q5UArbtOtMBCtxdICpfTCspRse+ywyXA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/array-each/-/array-each-1.0.1.tgz}
    name: array-each
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/array-slice/1.1.0:
    resolution: {integrity: sha512-B1qMD3RBP7O8o0H2KbrXDyB0IccejMF15+87Lvlor12ONPRHP6gTjXMNkt/d3ZuOGbAe66hFmaCfECI24Ufp6w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/array-slice/-/array-slice-1.1.0.tgz}
    name: array-slice
    version: 1.1.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/array-union/2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz}
    name: array-union
    version: 2.1.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/asynckit/0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz}
    name: asynckit
    version: 0.4.0
    dev: true

  registry.npmmirror.com/axios/1.1.3:
    resolution: {integrity: sha512-00tXVRwKx/FZr/IDVFt4C+f9FYairX517WoGCL6dpOntqLkZofjhu43F/Xl44UOpqa+9sLFDrG/XAnFsUYgkDA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/axios/-/axios-1.1.3.tgz}
    name: axios
    version: 1.1.3
    dependencies:
      follow-redirects: registry.npmmirror.com/follow-redirects/1.15.2
      form-data: registry.npmmirror.com/form-data/4.0.0
      proxy-from-env: registry.npmmirror.com/proxy-from-env/1.1.0
    transitivePeerDependencies:
      - debug
    dev: true

  registry.npmmirror.com/balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz}
    name: balanced-match
    version: 1.0.2
    dev: true

  registry.npmmirror.com/base64-js/1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz}
    name: base64-js
    version: 1.5.1
    dev: true

  registry.npmmirror.com/binary-extensions/2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz}
    name: binary-extensions
    version: 2.2.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/bl/4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz}
    name: bl
    version: 4.1.0
    dependencies:
      buffer: registry.npmmirror.com/buffer/5.7.1
      inherits: registry.npmmirror.com/inherits/2.0.4
      readable-stream: registry.npmmirror.com/readable-stream/3.6.0
    dev: true

  registry.npmmirror.com/bl/5.1.0:
    resolution: {integrity: sha512-tv1ZJHLfTDnXE6tMHv73YgSJaWR2AFuPwMntBe7XL/GBFHnT0CLnsHMogfk5+GzCDC5ZWarSCYaIGATZt9dNsQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/bl/-/bl-5.1.0.tgz}
    name: bl
    version: 5.1.0
    dependencies:
      buffer: registry.npmmirror.com/buffer/6.0.3
      inherits: registry.npmmirror.com/inherits/2.0.4
      readable-stream: registry.npmmirror.com/readable-stream/3.6.0
    dev: true

  registry.npmmirror.com/brace-expansion/1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz}
    name: brace-expansion
    version: 1.1.11
    dependencies:
      balanced-match: registry.npmmirror.com/balanced-match/1.0.2
      concat-map: registry.npmmirror.com/concat-map/0.0.1
    dev: true

  registry.npmmirror.com/braces/3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz}
    name: braces
    version: 3.0.2
    engines: {node: '>=8'}
    dependencies:
      fill-range: registry.npmmirror.com/fill-range/7.0.1
    dev: true

  registry.npmmirror.com/buffer/5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz}
    name: buffer
    version: 5.7.1
    dependencies:
      base64-js: registry.npmmirror.com/base64-js/1.5.1
      ieee754: registry.npmmirror.com/ieee754/1.2.1
    dev: true

  registry.npmmirror.com/buffer/6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/buffer/-/buffer-6.0.3.tgz}
    name: buffer
    version: 6.0.3
    dependencies:
      base64-js: registry.npmmirror.com/base64-js/1.5.1
      ieee754: registry.npmmirror.com/ieee754/1.2.1
    dev: true

  registry.npmmirror.com/busboy/1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/busboy/-/busboy-1.6.0.tgz}
    name: busboy
    version: 1.6.0
    engines: {node: '>=10.16.0'}
    dependencies:
      streamsearch: registry.npmmirror.com/streamsearch/1.1.0
    dev: true

  registry.npmmirror.com/cac/6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cac/-/cac-6.7.14.tgz}
    name: cac
    version: 6.7.14
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/camel-case/4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz}
    name: camel-case
    version: 4.1.2
    dependencies:
      pascal-case: registry.npmmirror.com/pascal-case/3.1.2
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/capital-case/1.0.4:
    resolution: {integrity: sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/capital-case/-/capital-case-1.0.4.tgz}
    name: capital-case
    version: 1.0.4
    dependencies:
      no-case: registry.npmmirror.com/no-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
      upper-case-first: registry.npmmirror.com/upper-case-first/2.0.2
    dev: true

  registry.npmmirror.com/chalk/2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz}
    name: chalk
    version: 2.4.2
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  registry.npmmirror.com/chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz}
    name: chalk
    version: 4.1.2
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles/4.3.0
      supports-color: registry.npmmirror.com/supports-color/7.2.0
    dev: true

  registry.npmmirror.com/chalk/5.1.2:
    resolution: {integrity: sha512-E5CkT4jWURs1Vy5qGJye+XwCkNj7Od3Af7CP6SujMetSMkLs8Do2RWJK5yx1wamHV/op8Rz+9rltjaTQWDnEFQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chalk/-/chalk-5.1.2.tgz}
    name: chalk
    version: 5.1.2
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}
    dev: true

  registry.npmmirror.com/change-case/4.1.2:
    resolution: {integrity: sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/change-case/-/change-case-4.1.2.tgz}
    name: change-case
    version: 4.1.2
    dependencies:
      camel-case: registry.npmmirror.com/camel-case/4.1.2
      capital-case: registry.npmmirror.com/capital-case/1.0.4
      constant-case: registry.npmmirror.com/constant-case/3.0.4
      dot-case: registry.npmmirror.com/dot-case/3.0.4
      header-case: registry.npmmirror.com/header-case/2.0.4
      no-case: registry.npmmirror.com/no-case/3.0.4
      param-case: registry.npmmirror.com/param-case/3.0.4
      pascal-case: registry.npmmirror.com/pascal-case/3.1.2
      path-case: registry.npmmirror.com/path-case/3.0.4
      sentence-case: registry.npmmirror.com/sentence-case/3.0.4
      snake-case: registry.npmmirror.com/snake-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/chardet/0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chardet/-/chardet-0.7.0.tgz}
    name: chardet
    version: 0.7.0
    dev: true

  registry.npmmirror.com/chokidar/3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz}
    name: chokidar
    version: 3.5.3
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: registry.npmmirror.com/anymatch/3.1.2
      braces: registry.npmmirror.com/braces/3.0.2
      glob-parent: registry.npmmirror.com/glob-parent/5.1.2
      is-binary-path: registry.npmmirror.com/is-binary-path/2.1.0
      is-glob: registry.npmmirror.com/is-glob/4.0.3
      normalize-path: registry.npmmirror.com/normalize-path/3.0.0
      readdirp: registry.npmmirror.com/readdirp/3.6.0
    optionalDependencies:
      fsevents: registry.npmmirror.com/fsevents/2.3.2
    dev: true

  registry.npmmirror.com/clean-stack/2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/clean-stack/-/clean-stack-2.2.0.tgz}
    name: clean-stack
    version: 2.2.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/cli-cursor/3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz}
    name: cli-cursor
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: registry.npmmirror.com/restore-cursor/3.1.0
    dev: true

  registry.npmmirror.com/cli-cursor/4.0.0:
    resolution: {integrity: sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-cursor/-/cli-cursor-4.0.0.tgz}
    name: cli-cursor
    version: 4.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      restore-cursor: registry.npmmirror.com/restore-cursor/4.0.0
    dev: true

  registry.npmmirror.com/cli-spinners/2.7.0:
    resolution: {integrity: sha512-qu3pN8Y3qHNgE2AFweciB1IfMnmZ/fsNTEE+NOFjmGB2F/7rLhnhzppvpCnN4FovtP26k8lHyy9ptEbNwWFLzw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-spinners/-/cli-spinners-2.7.0.tgz}
    name: cli-spinners
    version: 2.7.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/cli-width/3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cli-width/-/cli-width-3.0.0.tgz}
    name: cli-width
    version: 3.0.0
    engines: {node: '>= 10'}
    dev: true

  registry.npmmirror.com/clone/1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz}
    name: clone
    version: 1.0.4
    engines: {node: '>=0.8'}
    dev: true

  registry.npmmirror.com/color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz}
    name: color-convert
    version: 2.0.1
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: registry.npmmirror.com/color-name/1.1.4
    dev: true

  registry.npmmirror.com/color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz}
    name: color-name
    version: 1.1.4
    dev: true

  registry.npmmirror.com/colorette/2.0.19:
    resolution: {integrity: sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/colorette/-/colorette-2.0.19.tgz}
    name: colorette
    version: 2.0.19
    dev: true

  registry.npmmirror.com/combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz}
    name: combined-stream
    version: 1.0.8
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: registry.npmmirror.com/delayed-stream/1.0.0
    dev: true

  registry.npmmirror.com/concat-map/0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz}
    name: concat-map
    version: 0.0.1
    dev: true

  registry.npmmirror.com/consola/2.15.3:
    resolution: {integrity: sha512-9vAdYbHj6x2fLKC4+oPH0kFzY/orMZyG2Aj+kNylHxKGJ/Ed4dpNyAQYwJOdqO4zdM7XpVHmyejQDcQHrnuXbw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/consola/-/consola-2.15.3.tgz}
    name: consola
    version: 2.15.3
    dev: true

  registry.npmmirror.com/constant-case/3.0.4:
    resolution: {integrity: sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/constant-case/-/constant-case-3.0.4.tgz}
    name: constant-case
    version: 3.0.4
    dependencies:
      no-case: registry.npmmirror.com/no-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
      upper-case: registry.npmmirror.com/upper-case/2.0.2
    dev: true

  registry.npmmirror.com/cross-spawn/7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz}
    name: cross-spawn
    version: 7.0.3
    engines: {node: '>= 8'}
    dependencies:
      path-key: registry.npmmirror.com/path-key/3.1.1
      shebang-command: registry.npmmirror.com/shebang-command/2.0.0
      which: registry.npmmirror.com/which/2.0.2
    dev: true

  registry.npmmirror.com/css-tree/2.2.1:
    resolution: {integrity: sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/css-tree/-/css-tree-2.2.1.tgz}
    name: css-tree
    version: 2.2.1
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}
    dependencies:
      mdn-data: registry.npmmirror.com/mdn-data/2.0.28
      source-map-js: registry.npmmirror.com/source-map-js/1.0.2
    dev: true

  registry.npmmirror.com/debug/4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz}
    name: debug
    version: 4.3.4
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmmirror.com/ms/2.1.2
    dev: true

  registry.npmmirror.com/defaults/1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/defaults/-/defaults-1.0.4.tgz}
    name: defaults
    version: 1.0.4
    dependencies:
      clone: registry.npmmirror.com/clone/1.0.4
    dev: true

  registry.npmmirror.com/defu/6.1.1:
    resolution: {integrity: sha512-aA964RUCsBt0FGoNIlA3uFgo2hO+WWC0fiC6DBps/0SFzkKcYoM/3CzVLIa5xSsrFjdioMdYgAIbwo80qp2MoA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/defu/-/defu-6.1.1.tgz}
    name: defu
    version: 6.1.1
    dev: true

  registry.npmmirror.com/del/6.1.1:
    resolution: {integrity: sha512-ua8BhapfP0JUJKC/zV9yHHDW/rDoDxP4Zhn3AkA6/xT6gY7jYXJiaeyBZznYVujhZZET+UgcbZiQ7sN3WqcImg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/del/-/del-6.1.1.tgz}
    name: del
    version: 6.1.1
    engines: {node: '>=10'}
    dependencies:
      globby: registry.npmmirror.com/globby/11.1.0
      graceful-fs: registry.npmmirror.com/graceful-fs/4.2.10
      is-glob: registry.npmmirror.com/is-glob/4.0.3
      is-path-cwd: registry.npmmirror.com/is-path-cwd/2.2.0
      is-path-inside: registry.npmmirror.com/is-path-inside/3.0.3
      p-map: registry.npmmirror.com/p-map/4.0.0
      rimraf: registry.npmmirror.com/rimraf/3.0.2
      slash: registry.npmmirror.com/slash/3.0.0
    dev: true

  registry.npmmirror.com/delayed-stream/1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz}
    name: delayed-stream
    version: 1.0.0
    engines: {node: '>=0.4.0'}
    dev: true

  registry.npmmirror.com/destr/1.2.1:
    resolution: {integrity: sha512-ud8w0qMLlci6iFG7CNgeRr8OcbUWMsbfjtWft1eJ5Luqrz/M8Ebqk/KCzne8rKUlIQWWfLv0wD6QHrqOf4GshA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/destr/-/destr-1.2.1.tgz}
    name: destr
    version: 1.2.1
    dev: true

  registry.npmmirror.com/detect-file/1.0.0:
    resolution: {integrity: sha512-DtCOLG98P007x7wiiOmfI0fi3eIKyWiLTGJ2MDnVi/E04lWGbf+JzrRHMm0rgIIZJGtHpKpbVgLWHrv8xXpc3Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/detect-file/-/detect-file-1.0.0.tgz}
    name: detect-file
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/dir-glob/3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz}
    name: dir-glob
    version: 3.0.1
    engines: {node: '>=8'}
    dependencies:
      path-type: registry.npmmirror.com/path-type/4.0.0
    dev: true

  registry.npmmirror.com/dot-case/3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz}
    name: dot-case
    version: 3.0.4
    dependencies:
      no-case: registry.npmmirror.com/no-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/duplexer/0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/duplexer/-/duplexer-0.1.2.tgz}
    name: duplexer
    version: 0.1.2
    dev: true

  registry.npmmirror.com/emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz}
    name: emoji-regex
    version: 8.0.0
    dev: true

  registry.npmmirror.com/esbuild-android-64/0.15.13:
    resolution: {integrity: sha512-yRorukXBlokwTip+Sy4MYskLhJsO0Kn0/Fj43s1krVblfwP+hMD37a4Wmg139GEsMLl+vh8WXp2mq/cTA9J97g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-android-64/-/esbuild-android-64-0.15.13.tgz}
    name: esbuild-android-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-android-arm64/0.15.13:
    resolution: {integrity: sha512-TKzyymLD6PiVeyYa4c5wdPw87BeAiTXNtK6amWUcXZxkV51gOk5u5qzmDaYSwiWeecSNHamFsaFjLoi32QR5/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-android-arm64/-/esbuild-android-arm64-0.15.13.tgz}
    name: esbuild-android-arm64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-darwin-64/0.15.13:
    resolution: {integrity: sha512-WAx7c2DaOS6CrRcoYCgXgkXDliLnFv3pQLV6GeW1YcGEZq2Gnl8s9Pg7ahValZkpOa0iE/ojRVQ87sbUhF1Cbg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-darwin-64/-/esbuild-darwin-64-0.15.13.tgz}
    name: esbuild-darwin-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-darwin-arm64/0.15.13:
    resolution: {integrity: sha512-U6jFsPfSSxC3V1CLiQqwvDuj3GGrtQNB3P3nNC3+q99EKf94UGpsG9l4CQ83zBs1NHrk1rtCSYT0+KfK5LsD8A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.15.13.tgz}
    name: esbuild-darwin-arm64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-freebsd-64/0.15.13:
    resolution: {integrity: sha512-whItJgDiOXaDG/idy75qqevIpZjnReZkMGCgQaBWZuKHoElDJC1rh7MpoUgupMcdfOd+PgdEwNQW9DAE6i8wyA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.15.13.tgz}
    name: esbuild-freebsd-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-freebsd-arm64/0.15.13:
    resolution: {integrity: sha512-6pCSWt8mLUbPtygv7cufV0sZLeylaMwS5Fznj6Rsx9G2AJJsAjQ9ifA+0rQEIg7DwJmi9it+WjzNTEAzzdoM3Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.15.13.tgz}
    name: esbuild-freebsd-arm64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-32/0.15.13:
    resolution: {integrity: sha512-VbZdWOEdrJiYApm2kkxoTOgsoCO1krBZ3quHdYk3g3ivWaMwNIVPIfEE0f0XQQ0u5pJtBsnk2/7OPiCFIPOe/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-32/-/esbuild-linux-32-0.15.13.tgz}
    name: esbuild-linux-32
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-64/0.15.13:
    resolution: {integrity: sha512-rXmnArVNio6yANSqDQlIO4WiP+Cv7+9EuAHNnag7rByAqFVuRusLbGi2697A5dFPNXoO//IiogVwi3AdcfPC6A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-64/-/esbuild-linux-64-0.15.13.tgz}
    name: esbuild-linux-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-arm/0.15.13:
    resolution: {integrity: sha512-Ac6LpfmJO8WhCMQmO253xX2IU2B3wPDbl4IvR0hnqcPrdfCaUa2j/lLMGTjmQ4W5JsJIdHEdW12dG8lFS0MbxQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-arm/-/esbuild-linux-arm-0.15.13.tgz}
    name: esbuild-linux-arm
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-arm64/0.15.13:
    resolution: {integrity: sha512-alEMGU4Z+d17U7KQQw2IV8tQycO6T+rOrgW8OS22Ua25x6kHxoG6Ngry6Aq6uranC+pNWNMB6aHFPh7aTQdORQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.15.13.tgz}
    name: esbuild-linux-arm64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-mips64le/0.15.13:
    resolution: {integrity: sha512-47PgmyYEu+yN5rD/MbwS6DxP2FSGPo4Uxg5LwIdxTiyGC2XKwHhHyW7YYEDlSuXLQXEdTO7mYe8zQ74czP7W8A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.15.13.tgz}
    name: esbuild-linux-mips64le
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-ppc64le/0.15.13:
    resolution: {integrity: sha512-z6n28h2+PC1Ayle9DjKoBRcx/4cxHoOa2e689e2aDJSaKug3jXcQw7mM+GLg+9ydYoNzj8QxNL8ihOv/OnezhA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.15.13.tgz}
    name: esbuild-linux-ppc64le
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-riscv64/0.15.13:
    resolution: {integrity: sha512-+Lu4zuuXuQhgLUGyZloWCqTslcCAjMZH1k3Xc9MSEJEpEFdpsSU0sRDXAnk18FKOfEjhu4YMGaykx9xjtpA6ow==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.15.13.tgz}
    name: esbuild-linux-riscv64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-linux-s390x/0.15.13:
    resolution: {integrity: sha512-BMeXRljruf7J0TMxD5CIXS65y7puiZkAh+s4XFV9qy16SxOuMhxhVIXYLnbdfLrsYGFzx7U9mcdpFWkkvy/Uag==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.15.13.tgz}
    name: esbuild-linux-s390x
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-netbsd-64/0.15.13:
    resolution: {integrity: sha512-EHj9QZOTel581JPj7UO3xYbltFTYnHy+SIqJVq6yd3KkCrsHRbapiPb0Lx3EOOtybBEE9EyqbmfW1NlSDsSzvQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.15.13.tgz}
    name: esbuild-netbsd-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-openbsd-64/0.15.13:
    resolution: {integrity: sha512-nkuDlIjF/sfUhfx8SKq0+U+Fgx5K9JcPq1mUodnxI0x4kBdCv46rOGWbuJ6eof2n3wdoCLccOoJAbg9ba/bT2w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.15.13.tgz}
    name: esbuild-openbsd-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-sunos-64/0.15.13:
    resolution: {integrity: sha512-jVeu2GfxZQ++6lRdY43CS0Tm/r4WuQQ0Pdsrxbw+aOrHQPHV0+LNOLnvbN28M7BSUGnJnHkHm2HozGgNGyeIRw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-sunos-64/-/esbuild-sunos-64-0.15.13.tgz}
    name: esbuild-sunos-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-windows-32/0.15.13:
    resolution: {integrity: sha512-XoF2iBf0wnqo16SDq+aDGi/+QbaLFpkiRarPVssMh9KYbFNCqPLlGAWwDvxEVz+ywX6Si37J2AKm+AXq1kC0JA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-windows-32/-/esbuild-windows-32-0.15.13.tgz}
    name: esbuild-windows-32
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-windows-64/0.15.13:
    resolution: {integrity: sha512-Et6htEfGycjDrtqb2ng6nT+baesZPYQIW+HUEHK4D1ncggNrDNk3yoboYQ5KtiVrw/JaDMNttz8rrPubV/fvPQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-windows-64/-/esbuild-windows-64-0.15.13.tgz}
    name: esbuild-windows-64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/esbuild-windows-arm64/0.15.13:
    resolution: {integrity: sha512-3bv7tqntThQC9SWLRouMDmZnlOukBhOCTlkzNqzGCmrkCJI7io5LLjwJBOVY6kOUlIvdxbooNZwjtBvj+7uuVg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.15.13.tgz}
    name: esbuild-windows-arm64
    version: 0.15.13
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/escape-string-regexp/1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz}
    name: escape-string-regexp
    version: 1.0.5
    engines: {node: '>=0.8.0'}
    dev: true

  registry.npmmirror.com/estree-walker/2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz}
    name: estree-walker
    version: 2.0.2
    dev: true

  registry.npmmirror.com/execa/5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz}
    name: execa
    version: 5.1.1
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: registry.npmmirror.com/cross-spawn/7.0.3
      get-stream: registry.npmmirror.com/get-stream/6.0.1
      human-signals: registry.npmmirror.com/human-signals/2.1.0
      is-stream: registry.npmmirror.com/is-stream/2.0.1
      merge-stream: registry.npmmirror.com/merge-stream/2.0.0
      npm-run-path: registry.npmmirror.com/npm-run-path/4.0.1
      onetime: registry.npmmirror.com/onetime/5.1.2
      signal-exit: registry.npmmirror.com/signal-exit/3.0.7
      strip-final-newline: registry.npmmirror.com/strip-final-newline/2.0.0
    dev: true

  registry.npmmirror.com/expand-tilde/2.0.2:
    resolution: {integrity: sha512-A5EmesHW6rfnZ9ysHQjPdJRni0SRar0tjtG5MNtm9n5TUvsYU8oozprtRD4AqHxcZWWlVuAmQo2nWKfN9oyjTw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/expand-tilde/-/expand-tilde-2.0.2.tgz}
    name: expand-tilde
    version: 2.0.2
    engines: {node: '>=0.10.0'}
    dependencies:
      homedir-polyfill: registry.npmmirror.com/homedir-polyfill/1.0.3
    dev: true

  registry.npmmirror.com/extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz}
    name: extend
    version: 3.0.2
    dev: true

  registry.npmmirror.com/external-editor/3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/external-editor/-/external-editor-3.1.0.tgz}
    name: external-editor
    version: 3.1.0
    engines: {node: '>=4'}
    dependencies:
      chardet: registry.npmmirror.com/chardet/0.7.0
      iconv-lite: registry.npmmirror.com/iconv-lite/0.4.24
      tmp: registry.npmmirror.com/tmp/0.0.33
    dev: true

  registry.npmmirror.com/fast-glob/3.2.12:
    resolution: {integrity: sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fast-glob/-/fast-glob-3.2.12.tgz}
    name: fast-glob
    version: 3.2.12
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': registry.npmmirror.com/@nodelib/fs.stat/2.0.5
      '@nodelib/fs.walk': registry.npmmirror.com/@nodelib/fs.walk/1.2.8
      glob-parent: registry.npmmirror.com/glob-parent/5.1.2
      merge2: registry.npmmirror.com/merge2/1.4.1
      micromatch: registry.npmmirror.com/micromatch/4.0.5
    dev: true

  registry.npmmirror.com/fastq/1.13.0:
    resolution: {integrity: sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fastq/-/fastq-1.13.0.tgz}
    name: fastq
    version: 1.13.0
    dependencies:
      reusify: registry.npmmirror.com/reusify/1.0.4
    dev: true

  registry.npmmirror.com/figures/3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/figures/-/figures-3.2.0.tgz}
    name: figures
    version: 3.2.0
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: registry.npmmirror.com/escape-string-regexp/1.0.5
    dev: true

  registry.npmmirror.com/fill-range/7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz}
    name: fill-range
    version: 7.0.1
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: registry.npmmirror.com/to-regex-range/5.0.1
    dev: true

  registry.npmmirror.com/find-up/5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz}
    name: find-up
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      locate-path: registry.npmmirror.com/locate-path/6.0.0
      path-exists: registry.npmmirror.com/path-exists/4.0.0
    dev: true

  registry.npmmirror.com/findup-sync/5.0.0:
    resolution: {integrity: sha512-MzwXju70AuyflbgeOhzvQWAvvQdo1XL0A9bVvlXsYcFEBM87WR4OakL4OfZq+QRmr+duJubio+UtNQCPsVESzQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/findup-sync/-/findup-sync-5.0.0.tgz}
    name: findup-sync
    version: 5.0.0
    engines: {node: '>= 10.13.0'}
    dependencies:
      detect-file: registry.npmmirror.com/detect-file/1.0.0
      is-glob: registry.npmmirror.com/is-glob/4.0.3
      micromatch: registry.npmmirror.com/micromatch/4.0.5
      resolve-dir: registry.npmmirror.com/resolve-dir/1.0.1
    dev: true

  registry.npmmirror.com/fined/2.0.0:
    resolution: {integrity: sha512-OFRzsL6ZMHz5s0JrsEr+TpdGNCtrVtnuG3x1yzGNiQHT0yaDnXAj8V/lWcpJVrnoDpcwXcASxAZYbuXda2Y82A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fined/-/fined-2.0.0.tgz}
    name: fined
    version: 2.0.0
    engines: {node: '>= 10.13.0'}
    dependencies:
      expand-tilde: registry.npmmirror.com/expand-tilde/2.0.2
      is-plain-object: registry.npmmirror.com/is-plain-object/5.0.0
      object.defaults: registry.npmmirror.com/object.defaults/1.1.0
      object.pick: registry.npmmirror.com/object.pick/1.3.0
      parse-filepath: registry.npmmirror.com/parse-filepath/1.0.2
    dev: true

  registry.npmmirror.com/flagged-respawn/2.0.0:
    resolution: {integrity: sha512-Gq/a6YCi8zexmGHMuJwahTGzXlAZAOsbCVKduWXC6TlLCjjFRlExMJc4GC2NYPYZ0r/brw9P7CpRgQmlPVeOoA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/flagged-respawn/-/flagged-respawn-2.0.0.tgz}
    name: flagged-respawn
    version: 2.0.0
    engines: {node: '>= 10.13.0'}
    dev: true

  registry.npmmirror.com/follow-redirects/1.15.2:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz}
    name: follow-redirects
    version: 1.15.2
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: true

  registry.npmmirror.com/for-in/1.0.2:
    resolution: {integrity: sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/for-in/-/for-in-1.0.2.tgz}
    name: for-in
    version: 1.0.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/for-own/1.0.0:
    resolution: {integrity: sha512-0OABksIGrxKK8K4kynWkQ7y1zounQxP+CWnyclVwj81KW3vlLlGUx57DKGcP/LH216GzqnstnPocF16Nxs0Ycg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/for-own/-/for-own-1.0.0.tgz}
    name: for-own
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: registry.npmmirror.com/for-in/1.0.2
    dev: true

  registry.npmmirror.com/form-data/4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz}
    name: form-data
    version: 4.0.0
    engines: {node: '>= 6'}
    dependencies:
      asynckit: registry.npmmirror.com/asynckit/0.4.0
      combined-stream: registry.npmmirror.com/combined-stream/1.0.8
      mime-types: registry.npmmirror.com/mime-types/2.1.35
    dev: true

  registry.npmmirror.com/fs.realpath/1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz}
    name: fs.realpath
    version: 1.0.0
    dev: true

  registry.npmmirror.com/fsevents/2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz}
    name: fsevents
    version: 2.3.2
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/function-bind/1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz}
    name: function-bind
    version: 1.1.1
    dev: true

  registry.npmmirror.com/get-stream/6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz}
    name: get-stream
    version: 6.0.1
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz}
    name: glob-parent
    version: 5.1.2
    engines: {node: '>= 6'}
    dependencies:
      is-glob: registry.npmmirror.com/is-glob/4.0.3
    dev: true

  registry.npmmirror.com/glob/7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz}
    name: glob
    version: 7.2.3
    dependencies:
      fs.realpath: registry.npmmirror.com/fs.realpath/1.0.0
      inflight: registry.npmmirror.com/inflight/1.0.6
      inherits: registry.npmmirror.com/inherits/2.0.4
      minimatch: registry.npmmirror.com/minimatch/3.1.2
      once: registry.npmmirror.com/once/1.4.0
      path-is-absolute: registry.npmmirror.com/path-is-absolute/1.0.1
    dev: true

  registry.npmmirror.com/global-modules/1.0.0:
    resolution: {integrity: sha512-sKzpEkf11GpOFuw0Zzjzmt4B4UZwjOcG757PPvrfhxcLFbq0wpsgpOqxpxtxFiCG4DtG93M6XRVbF2oGdev7bg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/global-modules/-/global-modules-1.0.0.tgz}
    name: global-modules
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dependencies:
      global-prefix: registry.npmmirror.com/global-prefix/1.0.2
      is-windows: registry.npmmirror.com/is-windows/1.0.2
      resolve-dir: registry.npmmirror.com/resolve-dir/1.0.1
    dev: true

  registry.npmmirror.com/global-prefix/1.0.2:
    resolution: {integrity: sha512-5lsx1NUDHtSjfg0eHlmYvZKv8/nVqX4ckFbM+FrGcQ+04KWcWFo9P5MxPZYSzUvyzmdTbI7Eix8Q4IbELDqzKg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/global-prefix/-/global-prefix-1.0.2.tgz}
    name: global-prefix
    version: 1.0.2
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: registry.npmmirror.com/expand-tilde/2.0.2
      homedir-polyfill: registry.npmmirror.com/homedir-polyfill/1.0.3
      ini: registry.npmmirror.com/ini/1.3.8
      is-windows: registry.npmmirror.com/is-windows/1.0.2
      which: registry.npmmirror.com/which/1.3.1
    dev: true

  registry.npmmirror.com/globby/11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz}
    name: globby
    version: 11.1.0
    engines: {node: '>=10'}
    dependencies:
      array-union: registry.npmmirror.com/array-union/2.1.0
      dir-glob: registry.npmmirror.com/dir-glob/3.0.1
      fast-glob: registry.npmmirror.com/fast-glob/3.2.12
      ignore: registry.npmmirror.com/ignore/5.2.0
      merge2: registry.npmmirror.com/merge2/1.4.1
      slash: registry.npmmirror.com/slash/3.0.0
    dev: true

  registry.npmmirror.com/globby/13.1.2:
    resolution: {integrity: sha512-LKSDZXToac40u8Q1PQtZihbNdTYSNMuWe+K5l+oa6KgDzSvVrHXlJy40hUP522RjAIoNLJYBJi7ow+rbFpIhHQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/globby/-/globby-13.1.2.tgz}
    name: globby
    version: 13.1.2
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      dir-glob: registry.npmmirror.com/dir-glob/3.0.1
      fast-glob: registry.npmmirror.com/fast-glob/3.2.12
      ignore: registry.npmmirror.com/ignore/5.2.0
      merge2: registry.npmmirror.com/merge2/1.4.1
      slash: registry.npmmirror.com/slash/4.0.0
    dev: true

  registry.npmmirror.com/graceful-fs/4.2.10:
    resolution: {integrity: sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.10.tgz}
    name: graceful-fs
    version: 4.2.10
    dev: true

  registry.npmmirror.com/gzip-size/6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/gzip-size/-/gzip-size-6.0.0.tgz}
    name: gzip-size
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      duplexer: registry.npmmirror.com/duplexer/0.1.2
    dev: true

  registry.npmmirror.com/handlebars/4.7.7:
    resolution: {integrity: sha512-aAcXm5OAfE/8IXkcZvCepKU3VzW1/39Fb5ZuqMtgI/hT8X2YgoMvBY5dLhq/cpOvw7Lk1nK/UF71aLG/ZnVYRA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/handlebars/-/handlebars-4.7.7.tgz}
    name: handlebars
    version: 4.7.7
    engines: {node: '>=0.4.7'}
    hasBin: true
    dependencies:
      minimist: registry.npmmirror.com/minimist/1.2.7
      neo-async: registry.npmmirror.com/neo-async/2.6.2
      source-map: registry.npmmirror.com/source-map/0.6.1
      wordwrap: registry.npmmirror.com/wordwrap/1.0.0
    optionalDependencies:
      uglify-js: registry.npmmirror.com/uglify-js/3.17.4
    dev: true

  registry.npmmirror.com/has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz}
    name: has-flag
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/has/1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/has/-/has-1.0.3.tgz}
    name: has
    version: 1.0.3
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: registry.npmmirror.com/function-bind/1.1.1
    dev: true

  registry.npmmirror.com/header-case/2.0.4:
    resolution: {integrity: sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/header-case/-/header-case-2.0.4.tgz}
    name: header-case
    version: 2.0.4
    dependencies:
      capital-case: registry.npmmirror.com/capital-case/1.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/homedir-polyfill/1.0.3:
    resolution: {integrity: sha512-eSmmWE5bZTK2Nou4g0AI3zZ9rswp7GRKoKXS1BLUkvPviOqs4YTN1djQIqrXy9k5gEtdLPy86JjRwsNM9tnDcA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz}
    name: homedir-polyfill
    version: 1.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      parse-passwd: registry.npmmirror.com/parse-passwd/1.0.0
    dev: true

  registry.npmmirror.com/human-signals/2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz}
    name: human-signals
    version: 2.1.0
    engines: {node: '>=10.17.0'}
    dev: true

  registry.npmmirror.com/iconv-lite/0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz}
    name: iconv-lite
    version: 0.4.24
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: registry.npmmirror.com/safer-buffer/2.1.2
    dev: true

  registry.npmmirror.com/ieee754/1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz}
    name: ieee754
    version: 1.2.1
    dev: true

  registry.npmmirror.com/ignore/5.2.0:
    resolution: {integrity: sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ignore/-/ignore-5.2.0.tgz}
    name: ignore
    version: 5.2.0
    engines: {node: '>= 4'}
    dev: true

  registry.npmmirror.com/indent-string/4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz}
    name: indent-string
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/inflight/1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz}
    name: inflight
    version: 1.0.6
    dependencies:
      once: registry.npmmirror.com/once/1.4.0
      wrappy: registry.npmmirror.com/wrappy/1.0.2
    dev: true

  registry.npmmirror.com/inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz}
    name: inherits
    version: 2.0.4
    dev: true

  registry.npmmirror.com/ini/1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz}
    name: ini
    version: 1.3.8
    dev: true

  registry.npmmirror.com/inquirer/8.2.5:
    resolution: {integrity: sha512-QAgPDQMEgrDssk1XiwwHoOGYF9BAbUcc1+j+FhEvaOt8/cKRqyLn0U5qA6F74fGhTMGxf92pOvPBeh29jQJDTQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/inquirer/-/inquirer-8.2.5.tgz}
    name: inquirer
    version: 8.2.5
    engines: {node: '>=12.0.0'}
    dependencies:
      ansi-escapes: registry.npmmirror.com/ansi-escapes/4.3.2
      chalk: registry.npmmirror.com/chalk/4.1.2
      cli-cursor: registry.npmmirror.com/cli-cursor/3.1.0
      cli-width: registry.npmmirror.com/cli-width/3.0.0
      external-editor: registry.npmmirror.com/external-editor/3.1.0
      figures: registry.npmmirror.com/figures/3.2.0
      lodash: registry.npmmirror.com/lodash/4.17.21
      mute-stream: registry.npmmirror.com/mute-stream/0.0.8
      ora: registry.npmmirror.com/ora/5.4.1
      run-async: registry.npmmirror.com/run-async/2.4.1
      rxjs: registry.npmmirror.com/rxjs/7.5.7
      string-width: registry.npmmirror.com/string-width/4.2.3
      strip-ansi: registry.npmmirror.com/strip-ansi/6.0.1
      through: registry.npmmirror.com/through/2.3.8
      wrap-ansi: registry.npmmirror.com/wrap-ansi/7.0.0
    dev: true

  registry.npmmirror.com/interpret/2.2.0:
    resolution: {integrity: sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/interpret/-/interpret-2.2.0.tgz}
    name: interpret
    version: 2.2.0
    engines: {node: '>= 0.10'}
    dev: true

  registry.npmmirror.com/is-absolute/1.0.0:
    resolution: {integrity: sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-absolute/-/is-absolute-1.0.0.tgz}
    name: is-absolute
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dependencies:
      is-relative: registry.npmmirror.com/is-relative/1.0.0
      is-windows: registry.npmmirror.com/is-windows/1.0.2
    dev: true

  registry.npmmirror.com/is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz}
    name: is-binary-path
    version: 2.1.0
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: registry.npmmirror.com/binary-extensions/2.2.0
    dev: true

  registry.npmmirror.com/is-core-module/2.11.0:
    resolution: {integrity: sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-core-module/-/is-core-module-2.11.0.tgz}
    name: is-core-module
    version: 2.11.0
    dependencies:
      has: registry.npmmirror.com/has/1.0.3
    dev: true

  registry.npmmirror.com/is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz}
    name: is-extglob
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz}
    name: is-fullwidth-code-point
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz}
    name: is-glob
    version: 4.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: registry.npmmirror.com/is-extglob/2.1.1
    dev: true

  registry.npmmirror.com/is-interactive/1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-interactive/-/is-interactive-1.0.0.tgz}
    name: is-interactive
    version: 1.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/is-interactive/2.0.0:
    resolution: {integrity: sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-interactive/-/is-interactive-2.0.0.tgz}
    name: is-interactive
    version: 2.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz}
    name: is-number
    version: 7.0.0
    engines: {node: '>=0.12.0'}
    dev: true

  registry.npmmirror.com/is-path-cwd/2.2.0:
    resolution: {integrity: sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-path-cwd/-/is-path-cwd-2.2.0.tgz}
    name: is-path-cwd
    version: 2.2.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/is-path-inside/3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz}
    name: is-path-inside
    version: 3.0.3
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/is-plain-object/5.0.0:
    resolution: {integrity: sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-plain-object/-/is-plain-object-5.0.0.tgz}
    name: is-plain-object
    version: 5.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/is-relative/1.0.0:
    resolution: {integrity: sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-relative/-/is-relative-1.0.0.tgz}
    name: is-relative
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dependencies:
      is-unc-path: registry.npmmirror.com/is-unc-path/1.0.0
    dev: true

  registry.npmmirror.com/is-stream/2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz}
    name: is-stream
    version: 2.0.1
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/is-unc-path/1.0.0:
    resolution: {integrity: sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-unc-path/-/is-unc-path-1.0.0.tgz}
    name: is-unc-path
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dependencies:
      unc-path-regex: registry.npmmirror.com/unc-path-regex/0.1.2
    dev: true

  registry.npmmirror.com/is-unicode-supported/0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz}
    name: is-unicode-supported
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/is-unicode-supported/1.3.0:
    resolution: {integrity: sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz}
    name: is-unicode-supported
    version: 1.3.0
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/is-windows/1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/is-windows/-/is-windows-1.0.2.tgz}
    name: is-windows
    version: 1.0.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/isbinaryfile/4.0.10:
    resolution: {integrity: sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/isbinaryfile/-/isbinaryfile-4.0.10.tgz}
    name: isbinaryfile
    version: 4.0.10
    engines: {node: '>= 8.0.0'}
    dev: true

  registry.npmmirror.com/isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz}
    name: isexe
    version: 2.0.0
    dev: true

  registry.npmmirror.com/isobject/3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz}
    name: isobject
    version: 3.0.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/jiti/1.16.0:
    resolution: {integrity: sha512-L3BJStEf5NAqNuzrpfbN71dp43mYIcBUlCRea/vdyv5dW/AYa1d4bpelko4SHdY3I6eN9Wzyasxirj1/vv5kmg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/jiti/-/jiti-1.16.0.tgz}
    name: jiti
    version: 1.16.0
    hasBin: true
    dev: true

  registry.npmmirror.com/kind-of/6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz}
    name: kind-of
    version: 6.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/kolorist/1.6.0:
    resolution: {integrity: sha512-dLkz37Ab97HWMx9KTes3Tbi3D1ln9fCAy2zr2YVExJasDRPGRaKcoE4fycWNtnCAJfjFqe0cnY+f8KT2JePEXQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/kolorist/-/kolorist-1.6.0.tgz}
    name: kolorist
    version: 1.6.0
    dev: true

  registry.npmmirror.com/liftoff/4.0.0:
    resolution: {integrity: sha512-rMGwYF8q7g2XhG2ulBmmJgWv25qBsqRbDn5gH0+wnuyeFt7QBJlHJmtg5qEdn4pN6WVAUMgXnIxytMFRX9c1aA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/liftoff/-/liftoff-4.0.0.tgz}
    name: liftoff
    version: 4.0.0
    engines: {node: '>=10.13.0'}
    dependencies:
      extend: registry.npmmirror.com/extend/3.0.2
      findup-sync: registry.npmmirror.com/findup-sync/5.0.0
      fined: registry.npmmirror.com/fined/2.0.0
      flagged-respawn: registry.npmmirror.com/flagged-respawn/2.0.0
      is-plain-object: registry.npmmirror.com/is-plain-object/5.0.0
      object.map: registry.npmmirror.com/object.map/1.0.1
      rechoir: registry.npmmirror.com/rechoir/0.8.0
      resolve: registry.npmmirror.com/resolve/1.22.1
    dev: true

  registry.npmmirror.com/local-pkg/0.4.2:
    resolution: {integrity: sha512-mlERgSPrbxU3BP4qBqAvvwlgW4MTg78iwJdGGnv7kibKjWcJksrG3t6LB5lXI93wXRDvG4NpUgJFmTG4T6rdrg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.2.tgz}
    name: local-pkg
    version: 0.4.2
    engines: {node: '>=14'}
    dev: true

  registry.npmmirror.com/locate-path/6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz}
    name: locate-path
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      p-locate: registry.npmmirror.com/p-locate/5.0.0
    dev: true

  registry.npmmirror.com/lodash.get/4.4.2:
    resolution: {integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash.get/-/lodash.get-4.4.2.tgz}
    name: lodash.get
    version: 4.4.2
    dev: true

  registry.npmmirror.com/lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz}
    name: lodash
    version: 4.17.21
    dev: true

  registry.npmmirror.com/log-symbols/4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz}
    name: log-symbols
    version: 4.1.0
    engines: {node: '>=10'}
    dependencies:
      chalk: registry.npmmirror.com/chalk/4.1.2
      is-unicode-supported: registry.npmmirror.com/is-unicode-supported/0.1.0
    dev: true

  registry.npmmirror.com/log-symbols/5.1.0:
    resolution: {integrity: sha512-l0x2DvrW294C9uDCoQe1VSU4gf529FkSZ6leBl4TiqZH/e+0R7hSfHQBNut2mNygDgHwvYHfFLn6Oxb3VWj2rA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/log-symbols/-/log-symbols-5.1.0.tgz}
    name: log-symbols
    version: 5.1.0
    engines: {node: '>=12'}
    dependencies:
      chalk: registry.npmmirror.com/chalk/5.1.2
      is-unicode-supported: registry.npmmirror.com/is-unicode-supported/1.3.0
    dev: true

  registry.npmmirror.com/lower-case/2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz}
    name: lower-case
    version: 2.0.2
    dependencies:
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/magic-string/0.26.7:
    resolution: {integrity: sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/magic-string/-/magic-string-0.26.7.tgz}
    name: magic-string
    version: 0.26.7
    engines: {node: '>=12'}
    dependencies:
      sourcemap-codec: registry.npmmirror.com/sourcemap-codec/1.4.8
    dev: true

  registry.npmmirror.com/make-iterator/1.0.1:
    resolution: {integrity: sha512-pxiuXh0iVEq7VM7KMIhs5gxsfxCux2URptUQaXo4iZZJxBAzTPOLE2BumO5dbfVYq/hBJFBR/a1mFDmOx5AGmw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/make-iterator/-/make-iterator-1.0.1.tgz}
    name: make-iterator
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: registry.npmmirror.com/kind-of/6.0.3
    dev: true

  registry.npmmirror.com/map-cache/0.2.2:
    resolution: {integrity: sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/map-cache/-/map-cache-0.2.2.tgz}
    name: map-cache
    version: 0.2.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/mdn-data/2.0.28:
    resolution: {integrity: sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mdn-data/-/mdn-data-2.0.28.tgz}
    name: mdn-data
    version: 2.0.28
    dev: true

  registry.npmmirror.com/merge-stream/2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz}
    name: merge-stream
    version: 2.0.0
    dev: true

  registry.npmmirror.com/merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz}
    name: merge2
    version: 1.4.1
    engines: {node: '>= 8'}
    dev: true

  registry.npmmirror.com/micromatch/4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz}
    name: micromatch
    version: 4.0.5
    engines: {node: '>=8.6'}
    dependencies:
      braces: registry.npmmirror.com/braces/3.0.2
      picomatch: registry.npmmirror.com/picomatch/2.3.1
    dev: true

  registry.npmmirror.com/mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz}
    name: mime-db
    version: 1.52.0
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmmirror.com/mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz}
    name: mime-types
    version: 2.1.35
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: registry.npmmirror.com/mime-db/1.52.0
    dev: true

  registry.npmmirror.com/mimic-fn/2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz}
    name: mimic-fn
    version: 2.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz}
    name: minimatch
    version: 3.1.2
    dependencies:
      brace-expansion: registry.npmmirror.com/brace-expansion/1.1.11
    dev: true

  registry.npmmirror.com/minimist/1.2.7:
    resolution: {integrity: sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/minimist/-/minimist-1.2.7.tgz}
    name: minimist
    version: 1.2.7
    dev: true

  registry.npmmirror.com/mkdirp/1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.4.tgz}
    name: mkdirp
    version: 1.0.4
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  registry.npmmirror.com/mrmime/1.0.1:
    resolution: {integrity: sha512-hzzEagAgDyoU1Q6yg5uI+AorQgdvMCur3FcKf7NhMKWsaYg+RnbTyHRa/9IlLF9rf455MOCtcqqrQQ83pPP7Uw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mrmime/-/mrmime-1.0.1.tgz}
    name: mrmime
    version: 1.0.1
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz}
    name: ms
    version: 2.1.2
    dev: true

  registry.npmmirror.com/mute-stream/0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/mute-stream/-/mute-stream-0.0.8.tgz}
    name: mute-stream
    version: 0.0.8
    dev: true

  registry.npmmirror.com/neo-async/2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz}
    name: neo-async
    version: 2.6.2
    dev: true

  registry.npmmirror.com/no-case/3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz}
    name: no-case
    version: 3.0.4
    dependencies:
      lower-case: registry.npmmirror.com/lower-case/2.0.2
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/node-fetch-native/0.1.8:
    resolution: {integrity: sha512-ZNaury9r0NxaT2oL65GvdGDy+5PlSaHTovT6JV5tOW07k1TQmgC0olZETa4C9KZg0+6zBr99ctTYa3Utqj9P/Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-fetch-native/-/node-fetch-native-0.1.8.tgz}
    name: node-fetch-native
    version: 0.1.8
    dev: true

  registry.npmmirror.com/node-plop/0.31.0:
    resolution: {integrity: sha512-aKLPxiBoFTNUovvtK8j/Whc4PZREkYx6htw2HJPiU8wYquXmN8pkd9B3xlFo6AJ4ZlzFsQSf/NXR5xET8EqRYw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/node-plop/-/node-plop-0.31.0.tgz}
    name: node-plop
    version: 0.31.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      '@types/inquirer': registry.npmmirror.com/@types/inquirer/8.2.5
      change-case: registry.npmmirror.com/change-case/4.1.2
      del: registry.npmmirror.com/del/6.1.1
      globby: registry.npmmirror.com/globby/13.1.2
      handlebars: registry.npmmirror.com/handlebars/4.7.7
      inquirer: registry.npmmirror.com/inquirer/8.2.5
      isbinaryfile: registry.npmmirror.com/isbinaryfile/4.0.10
      lodash.get: registry.npmmirror.com/lodash.get/4.4.2
      lower-case: registry.npmmirror.com/lower-case/2.0.2
      mkdirp: registry.npmmirror.com/mkdirp/1.0.4
      resolve: registry.npmmirror.com/resolve/1.22.1
      title-case: registry.npmmirror.com/title-case/3.0.3
      upper-case: registry.npmmirror.com/upper-case/2.0.2
    dev: true

  registry.npmmirror.com/normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz}
    name: normalize-path
    version: 3.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/npm-run-path/4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz}
    name: npm-run-path
    version: 4.0.1
    engines: {node: '>=8'}
    dependencies:
      path-key: registry.npmmirror.com/path-key/3.1.1
    dev: true

  registry.npmmirror.com/object.defaults/1.1.0:
    resolution: {integrity: sha512-c/K0mw/F11k4dEUBMW8naXUuBuhxRCfG7W+yFy8EcijU/rSmazOUd1XAEEe6bC0OuXY4HUKjTJv7xbxIMqdxrA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/object.defaults/-/object.defaults-1.1.0.tgz}
    name: object.defaults
    version: 1.1.0
    engines: {node: '>=0.10.0'}
    dependencies:
      array-each: registry.npmmirror.com/array-each/1.0.1
      array-slice: registry.npmmirror.com/array-slice/1.1.0
      for-own: registry.npmmirror.com/for-own/1.0.0
      isobject: registry.npmmirror.com/isobject/3.0.1
    dev: true

  registry.npmmirror.com/object.map/1.0.1:
    resolution: {integrity: sha512-3+mAJu2PLfnSVGHwIWubpOFLscJANBKuB/6A4CxBstc4aqwQY0FWcsppuy4jU5GSB95yES5JHSI+33AWuS4k6w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/object.map/-/object.map-1.0.1.tgz}
    name: object.map
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dependencies:
      for-own: registry.npmmirror.com/for-own/1.0.0
      make-iterator: registry.npmmirror.com/make-iterator/1.0.1
    dev: true

  registry.npmmirror.com/object.pick/1.3.0:
    resolution: {integrity: sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/object.pick/-/object.pick-1.3.0.tgz}
    name: object.pick
    version: 1.3.0
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: registry.npmmirror.com/isobject/3.0.1
    dev: true

  registry.npmmirror.com/ohmyfetch/0.4.21:
    resolution: {integrity: sha512-VG7f/JRvqvBOYvL0tHyEIEG7XHWm7OqIfAs6/HqwWwDfjiJ1g0huIpe5sFEmyb+7hpFa1EGNH2aERWR72tlClw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ohmyfetch/-/ohmyfetch-0.4.21.tgz}
    name: ohmyfetch
    version: 0.4.21
    dependencies:
      destr: registry.npmmirror.com/destr/1.2.1
      node-fetch-native: registry.npmmirror.com/node-fetch-native/0.1.8
      ufo: registry.npmmirror.com/ufo/0.8.6
      undici: registry.npmmirror.com/undici/5.12.0
    dev: true

  registry.npmmirror.com/once/1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/once/-/once-1.4.0.tgz}
    name: once
    version: 1.4.0
    dependencies:
      wrappy: registry.npmmirror.com/wrappy/1.0.2
    dev: true

  registry.npmmirror.com/onetime/5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz}
    name: onetime
    version: 5.1.2
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: registry.npmmirror.com/mimic-fn/2.1.0
    dev: true

  registry.npmmirror.com/ora/5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ora/-/ora-5.4.1.tgz}
    name: ora
    version: 5.4.1
    engines: {node: '>=10'}
    dependencies:
      bl: registry.npmmirror.com/bl/4.1.0
      chalk: registry.npmmirror.com/chalk/4.1.2
      cli-cursor: registry.npmmirror.com/cli-cursor/3.1.0
      cli-spinners: registry.npmmirror.com/cli-spinners/2.7.0
      is-interactive: registry.npmmirror.com/is-interactive/1.0.0
      is-unicode-supported: registry.npmmirror.com/is-unicode-supported/0.1.0
      log-symbols: registry.npmmirror.com/log-symbols/4.1.0
      strip-ansi: registry.npmmirror.com/strip-ansi/6.0.1
      wcwidth: registry.npmmirror.com/wcwidth/1.0.1
    dev: true

  registry.npmmirror.com/ora/6.1.2:
    resolution: {integrity: sha512-EJQ3NiP5Xo94wJXIzAyOtSb0QEIAUu7m8t6UZ9krbz0vAJqr92JpcK/lEXg91q6B9pEGqrykkd2EQplnifDSBw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ora/-/ora-6.1.2.tgz}
    name: ora
    version: 6.1.2
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      bl: registry.npmmirror.com/bl/5.1.0
      chalk: registry.npmmirror.com/chalk/5.1.2
      cli-cursor: registry.npmmirror.com/cli-cursor/4.0.0
      cli-spinners: registry.npmmirror.com/cli-spinners/2.7.0
      is-interactive: registry.npmmirror.com/is-interactive/2.0.0
      is-unicode-supported: registry.npmmirror.com/is-unicode-supported/1.3.0
      log-symbols: registry.npmmirror.com/log-symbols/5.1.0
      strip-ansi: registry.npmmirror.com/strip-ansi/7.0.1
      wcwidth: registry.npmmirror.com/wcwidth/1.0.1
    dev: true

  registry.npmmirror.com/os-tmpdir/1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz}
    name: os-tmpdir
    version: 1.0.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/p-limit/3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz}
    name: p-limit
    version: 3.1.0
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: registry.npmmirror.com/yocto-queue/0.1.0
    dev: true

  registry.npmmirror.com/p-locate/5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz}
    name: p-locate
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      p-limit: registry.npmmirror.com/p-limit/3.1.0
    dev: true

  registry.npmmirror.com/p-map/4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz}
    name: p-map
    version: 4.0.0
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: registry.npmmirror.com/aggregate-error/3.1.0
    dev: true

  registry.npmmirror.com/param-case/3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz}
    name: param-case
    version: 3.0.4
    dependencies:
      dot-case: registry.npmmirror.com/dot-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/parse-filepath/1.0.2:
    resolution: {integrity: sha512-FwdRXKCohSVeXqwtYonZTXtbGJKrn+HNyWDYVcp5yuJlesTwNH4rsmRZ+GrKAPJ5bLpRxESMeS+Rl0VCHRvB2Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/parse-filepath/-/parse-filepath-1.0.2.tgz}
    name: parse-filepath
    version: 1.0.2
    engines: {node: '>=0.8'}
    dependencies:
      is-absolute: registry.npmmirror.com/is-absolute/1.0.0
      map-cache: registry.npmmirror.com/map-cache/0.2.2
      path-root: registry.npmmirror.com/path-root/0.1.1
    dev: true

  registry.npmmirror.com/parse-passwd/1.0.0:
    resolution: {integrity: sha512-1Y1A//QUXEZK7YKz+rD9WydcE1+EuPr6ZBgKecAB8tmoW6UFv0NREVJe1p+jRxtThkcbbKkfwIbWJe/IeE6m2Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/parse-passwd/-/parse-passwd-1.0.0.tgz}
    name: parse-passwd
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/pascal-case/3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz}
    name: pascal-case
    version: 3.1.2
    dependencies:
      no-case: registry.npmmirror.com/no-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/path-case/3.0.4:
    resolution: {integrity: sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-case/-/path-case-3.0.4.tgz}
    name: path-case
    version: 3.0.4
    dependencies:
      dot-case: registry.npmmirror.com/dot-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz}
    name: path-exists
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/path-is-absolute/1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz}
    name: path-is-absolute
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz}
    name: path-key
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz}
    name: path-parse
    version: 1.0.7
    dev: true

  registry.npmmirror.com/path-root-regex/0.1.2:
    resolution: {integrity: sha512-4GlJ6rZDhQZFE0DPVKh0e9jmZ5egZfxTkp7bcRDuPlJXbAwhxcl2dINPUAsjLdejqaLsCeg8axcLjIbvBjN4pQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-root-regex/-/path-root-regex-0.1.2.tgz}
    name: path-root-regex
    version: 0.1.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/path-root/0.1.1:
    resolution: {integrity: sha512-QLcPegTHF11axjfojBIoDygmS2E3Lf+8+jI6wOVmNVenrKSo3mFdSGiIgdSHenczw3wPtlVMQaFVwGmM7BJdtg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-root/-/path-root-0.1.1.tgz}
    name: path-root
    version: 0.1.1
    engines: {node: '>=0.10.0'}
    dependencies:
      path-root-regex: registry.npmmirror.com/path-root-regex/0.1.2
    dev: true

  registry.npmmirror.com/path-type/4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz}
    name: path-type
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/pathe/0.3.9:
    resolution: {integrity: sha512-6Y6s0vT112P3jD8dGfuS6r+lpa0qqNrLyHPOwvXMnyNTQaYiwgau2DP3aNDsR13xqtGj7rrPo+jFUATpU6/s+g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/pathe/-/pathe-0.3.9.tgz}
    name: pathe
    version: 0.3.9
    dev: true

  registry.npmmirror.com/perfect-debounce/0.1.3:
    resolution: {integrity: sha512-NOT9AcKiDGpnV/HBhI22Str++XWcErO/bALvHCuhv33owZW/CjH8KAFLZDCmu3727sihe0wTxpDhyGc6M8qacQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/perfect-debounce/-/perfect-debounce-0.1.3.tgz}
    name: perfect-debounce
    version: 0.1.3
    dev: true

  registry.npmmirror.com/picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz}
    name: picomatch
    version: 2.3.1
    engines: {node: '>=8.6'}
    dev: true

  registry.npmmirror.com/plop/3.1.1:
    resolution: {integrity: sha512-NuctKmuNUACXBQn25bBr5oj/75nHxdKGwjA/+b7cVoj1sp+gTVqcc8eAr4QcNJgMPsZWRJBN2kMkgmsqbqV9gg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/plop/-/plop-3.1.1.tgz}
    name: plop
    version: 3.1.1
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true
    dependencies:
      '@types/liftoff': registry.npmmirror.com/@types/liftoff/4.0.0
      chalk: registry.npmmirror.com/chalk/5.1.2
      interpret: registry.npmmirror.com/interpret/2.2.0
      liftoff: registry.npmmirror.com/liftoff/4.0.0
      minimist: registry.npmmirror.com/minimist/1.2.7
      node-plop: registry.npmmirror.com/node-plop/0.31.0
      ora: registry.npmmirror.com/ora/6.1.2
      v8flags: registry.npmmirror.com/v8flags/4.0.0
    dev: true

  registry.npmmirror.com/prettier/2.7.1:
    resolution: {integrity: sha512-ujppO+MkdPqoVINuDFDRLClm7D78qbDt0/NR+wp5FqEZOoTNAjPHWj17QRhu7geIHJfcNhRk1XVQmF8Bp3ye+g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/prettier/-/prettier-2.7.1.tgz}
    name: prettier
    version: 2.7.1
    engines: {node: '>=10.13.0'}
    hasBin: true
    dev: true
    optional: true

  registry.npmmirror.com/proxy-from-env/1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz}
    name: proxy-from-env
    version: 1.1.0
    dev: true

  registry.npmmirror.com/queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz}
    name: queue-microtask
    version: 1.2.3
    dev: true

  registry.npmmirror.com/readable-stream/3.6.0:
    resolution: {integrity: sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.0.tgz}
    name: readable-stream
    version: 3.6.0
    engines: {node: '>= 6'}
    dependencies:
      inherits: registry.npmmirror.com/inherits/2.0.4
      string_decoder: registry.npmmirror.com/string_decoder/1.3.0
      util-deprecate: registry.npmmirror.com/util-deprecate/1.0.2
    dev: true

  registry.npmmirror.com/readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz}
    name: readdirp
    version: 3.6.0
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: registry.npmmirror.com/picomatch/2.3.1
    dev: true

  registry.npmmirror.com/rechoir/0.8.0:
    resolution: {integrity: sha512-/vxpCXddiX8NGfGO/mTafwjq4aFa/71pvamip0++IQk3zG8cbCj0fifNPrjjF1XMXUne91jL9OoxmdykoEtifQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rechoir/-/rechoir-0.8.0.tgz}
    name: rechoir
    version: 0.8.0
    engines: {node: '>= 10.13.0'}
    dependencies:
      resolve: registry.npmmirror.com/resolve/1.22.1
    dev: true

  registry.npmmirror.com/resolve-dir/1.0.1:
    resolution: {integrity: sha512-R7uiTjECzvOsWSfdM0QKFNBVFcK27aHOUwdvK53BcW8zqnGdYp0Fbj82cy54+2A4P2tFM22J5kRfe1R+lM/1yg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve-dir/-/resolve-dir-1.0.1.tgz}
    name: resolve-dir
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dependencies:
      expand-tilde: registry.npmmirror.com/expand-tilde/2.0.2
      global-modules: registry.npmmirror.com/global-modules/1.0.0
    dev: true

  registry.npmmirror.com/resolve/1.22.1:
    resolution: {integrity: sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/resolve/-/resolve-1.22.1.tgz}
    name: resolve
    version: 1.22.1
    hasBin: true
    dependencies:
      is-core-module: registry.npmmirror.com/is-core-module/2.11.0
      path-parse: registry.npmmirror.com/path-parse/1.0.7
      supports-preserve-symlinks-flag: registry.npmmirror.com/supports-preserve-symlinks-flag/1.0.0
    dev: true

  registry.npmmirror.com/restore-cursor/3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz}
    name: restore-cursor
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      onetime: registry.npmmirror.com/onetime/5.1.2
      signal-exit: registry.npmmirror.com/signal-exit/3.0.7
    dev: true

  registry.npmmirror.com/restore-cursor/4.0.0:
    resolution: {integrity: sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/restore-cursor/-/restore-cursor-4.0.0.tgz}
    name: restore-cursor
    version: 4.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      onetime: registry.npmmirror.com/onetime/5.1.2
      signal-exit: registry.npmmirror.com/signal-exit/3.0.7
    dev: true

  registry.npmmirror.com/reusify/1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz}
    name: reusify
    version: 1.0.4
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz}
    name: rimraf
    version: 3.0.2
    hasBin: true
    dependencies:
      glob: registry.npmmirror.com/glob/7.2.3
    dev: true

  registry.npmmirror.com/run-async/2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/run-async/-/run-async-2.4.1.tgz}
    name: run-async
    version: 2.4.1
    engines: {node: '>=0.12.0'}
    dev: true

  registry.npmmirror.com/run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz}
    name: run-parallel
    version: 1.2.0
    dependencies:
      queue-microtask: registry.npmmirror.com/queue-microtask/1.2.3
    dev: true

  registry.npmmirror.com/rxjs/7.5.7:
    resolution: {integrity: sha512-z9MzKh/UcOqB3i20H6rtrlaE/CgjLOvheWK/9ILrbhROGTweAi1BaFsTT9FbwZi5Trr1qNRs+MXkhmR06awzQA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/rxjs/-/rxjs-7.5.7.tgz}
    name: rxjs
    version: 7.5.7
    dependencies:
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/safe-buffer/5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz}
    name: safe-buffer
    version: 5.2.1
    dev: true

  registry.npmmirror.com/safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz}
    name: safer-buffer
    version: 2.1.2
    dev: true

  registry.npmmirror.com/sentence-case/3.0.4:
    resolution: {integrity: sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sentence-case/-/sentence-case-3.0.4.tgz}
    name: sentence-case
    version: 3.0.4
    dependencies:
      no-case: registry.npmmirror.com/no-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
      upper-case-first: registry.npmmirror.com/upper-case-first/2.0.2
    dev: true

  registry.npmmirror.com/shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz}
    name: shebang-command
    version: 2.0.0
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: registry.npmmirror.com/shebang-regex/3.0.0
    dev: true

  registry.npmmirror.com/shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz}
    name: shebang-regex
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/signal-exit/3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz}
    name: signal-exit
    version: 3.0.7
    dev: true

  registry.npmmirror.com/sirv/2.0.2:
    resolution: {integrity: sha512-4Qog6aE29nIjAOKe/wowFTxOdmbEZKb+3tsLljaBRzJwtqto0BChD2zzH0LhgCSXiI+V7X+Y45v14wBZQ1TK3w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sirv/-/sirv-2.0.2.tgz}
    name: sirv
    version: 2.0.2
    engines: {node: '>= 10'}
    dependencies:
      '@polka/url': registry.npmmirror.com/@polka/url/1.0.0-next.21
      mrmime: registry.npmmirror.com/mrmime/1.0.1
      totalist: registry.npmmirror.com/totalist/3.0.0
    dev: true

  registry.npmmirror.com/slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz}
    name: slash
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmmirror.com/slash/4.0.0:
    resolution: {integrity: sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/slash/-/slash-4.0.0.tgz}
    name: slash
    version: 4.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmmirror.com/snake-case/3.0.4:
    resolution: {integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/snake-case/-/snake-case-3.0.4.tgz}
    name: snake-case
    version: 3.0.4
    dependencies:
      dot-case: registry.npmmirror.com/dot-case/3.0.4
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/source-map-js/1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz}
    name: source-map-js
    version: 1.0.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz}
    name: source-map
    version: 0.6.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/sourcemap-codec/1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz}
    name: sourcemap-codec
    version: 1.4.8
    dev: true

  registry.npmmirror.com/streamsearch/1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/streamsearch/-/streamsearch-1.1.0.tgz}
    name: streamsearch
    version: 1.1.0
    engines: {node: '>=10.0.0'}
    dev: true

  registry.npmmirror.com/string-width/4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz}
    name: string-width
    version: 4.2.3
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: registry.npmmirror.com/emoji-regex/8.0.0
      is-fullwidth-code-point: registry.npmmirror.com/is-fullwidth-code-point/3.0.0
      strip-ansi: registry.npmmirror.com/strip-ansi/6.0.1
    dev: true

  registry.npmmirror.com/string_decoder/1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz}
    name: string_decoder
    version: 1.3.0
    dependencies:
      safe-buffer: registry.npmmirror.com/safe-buffer/5.2.1
    dev: true

  registry.npmmirror.com/strip-ansi/6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz}
    name: strip-ansi
    version: 6.0.1
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: registry.npmmirror.com/ansi-regex/5.0.1
    dev: true

  registry.npmmirror.com/strip-ansi/7.0.1:
    resolution: {integrity: sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.0.1.tgz}
    name: strip-ansi
    version: 7.0.1
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: registry.npmmirror.com/ansi-regex/6.0.1
    dev: true

  registry.npmmirror.com/strip-final-newline/2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz}
    name: strip-final-newline
    version: 2.0.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz}
    name: supports-color
    version: 7.2.0
    engines: {node: '>=8'}
    dependencies:
      has-flag: registry.npmmirror.com/has-flag/4.0.0
    dev: true

  registry.npmmirror.com/supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz}
    name: supports-preserve-symlinks-flag
    version: 1.0.0
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmmirror.com/through/2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/through/-/through-2.3.8.tgz}
    name: through
    version: 2.3.8
    dev: true

  registry.npmmirror.com/title-case/3.0.3:
    resolution: {integrity: sha512-e1zGYRvbffpcHIrnuqT0Dh+gEJtDaxDSoG4JAIpq4oDFyooziLBIiYQv0GBT4FUAnUop5uZ1hiIAj7oAF6sOCA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/title-case/-/title-case-3.0.3.tgz}
    name: title-case
    version: 3.0.3
    dependencies:
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/tmp/0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tmp/-/tmp-0.0.33.tgz}
    name: tmp
    version: 0.0.33
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: registry.npmmirror.com/os-tmpdir/1.0.2
    dev: true

  registry.npmmirror.com/to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz}
    name: to-regex-range
    version: 5.0.1
    engines: {node: '>=8.0'}
    dependencies:
      is-number: registry.npmmirror.com/is-number/7.0.0
    dev: true

  registry.npmmirror.com/totalist/3.0.0:
    resolution: {integrity: sha512-eM+pCBxXO/njtF7vdFsHuqb+ElbxqtI4r5EAvk6grfAFyJ6IvWlSkfZ5T9ozC6xWw3Fj1fGoSmrl0gUs46JVIw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/totalist/-/totalist-3.0.0.tgz}
    name: totalist
    version: 3.0.0
    engines: {node: '>=6'}
    dev: true

  registry.npmmirror.com/tslib/2.4.1:
    resolution: {integrity: sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/tslib/-/tslib-2.4.1.tgz}
    name: tslib
    version: 2.4.1
    dev: true

  registry.npmmirror.com/type-fest/0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz}
    name: type-fest
    version: 0.21.3
    engines: {node: '>=10'}
    dev: true

  registry.npmmirror.com/ufo/0.8.6:
    resolution: {integrity: sha512-fk6CmUgwKCfX79EzcDQQpSCMxrHstvbLswFChHS0Vump+kFkw7nJBfTZoC1j0bOGoY9I7R3n2DGek5ajbcYnOw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/ufo/-/ufo-0.8.6.tgz}
    name: ufo
    version: 0.8.6
    dev: true

  registry.npmmirror.com/uglify-js/3.17.4:
    resolution: {integrity: sha512-T9q82TJI9e/C1TAxYvfb16xO120tMVFZrGA3f9/P4424DNu6ypK103y0GPFVa17yotwSyZW5iYXgjYHkGrJW/g==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/uglify-js/-/uglify-js-3.17.4.tgz}
    name: uglify-js
    version: 3.17.4
    engines: {node: '>=0.8.0'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  registry.npmmirror.com/unc-path-regex/0.1.2:
    resolution: {integrity: sha512-eXL4nmJT7oCpkZsHZUOJo8hcX3GbsiDOa0Qu9F646fi8dT3XuSVopVqAcEiVzSKKH7UoDti23wNX3qGFxcW5Qg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/unc-path-regex/-/unc-path-regex-0.1.2.tgz}
    name: unc-path-regex
    version: 0.1.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmmirror.com/unconfig/0.3.7:
    resolution: {integrity: sha512-1589b7oGa8ILBYpta7TndM5mLHLzHUqBfhszeZxuUBrjO/RoQ52VGVWsS3w0C0GLNxO9RPmqkf6BmIvBApaRdA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/unconfig/-/unconfig-0.3.7.tgz}
    name: unconfig
    version: 0.3.7
    dependencies:
      '@antfu/utils': registry.npmmirror.com/@antfu/utils/0.5.2
      defu: registry.npmmirror.com/defu/6.1.1
      jiti: registry.npmmirror.com/jiti/1.16.0
    dev: true

  registry.npmmirror.com/undici/5.12.0:
    resolution: {integrity: sha512-zMLamCG62PGjd9HHMpo05bSLvvwWOZgGeiWlN/vlqu3+lRo3elxktVGEyLMX+IO7c2eflLjcW74AlkhEZm15mg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/undici/-/undici-5.12.0.tgz}
    name: undici
    version: 5.12.0
    engines: {node: '>=12.18'}
    dependencies:
      busboy: registry.npmmirror.com/busboy/1.6.0
    dev: true

  registry.npmmirror.com/unocss/0.46.5_vite@3.2.3:
    resolution: {integrity: sha512-AMURkxzvhHgjxD9KcEyo1y+zTJvjHG9O+vYWTyyMEQeCspFmGONoEtnBC9p8MGPsiW4afSEJCrIROOEl186zKQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/unocss/-/unocss-0.46.5.tgz}
    id: registry.npmmirror.com/unocss/0.46.5
    name: unocss
    version: 0.46.5
    engines: {node: '>=14'}
    peerDependencies:
      '@unocss/webpack': 0.46.5
    peerDependenciesMeta:
      '@unocss/webpack':
        optional: true
    dependencies:
      '@unocss/astro': registry.npmmirror.com/@unocss/astro/0.46.5_vite@3.2.3
      '@unocss/cli': registry.npmmirror.com/@unocss/cli/0.46.5
      '@unocss/core': registry.npmmirror.com/@unocss/core/0.46.5
      '@unocss/preset-attributify': registry.npmmirror.com/@unocss/preset-attributify/0.46.5
      '@unocss/preset-icons': registry.npmmirror.com/@unocss/preset-icons/0.46.5
      '@unocss/preset-mini': registry.npmmirror.com/@unocss/preset-mini/0.46.5
      '@unocss/preset-tagify': registry.npmmirror.com/@unocss/preset-tagify/0.46.5
      '@unocss/preset-typography': registry.npmmirror.com/@unocss/preset-typography/0.46.5
      '@unocss/preset-uno': registry.npmmirror.com/@unocss/preset-uno/0.46.5
      '@unocss/preset-web-fonts': registry.npmmirror.com/@unocss/preset-web-fonts/0.46.5
      '@unocss/preset-wind': registry.npmmirror.com/@unocss/preset-wind/0.46.5
      '@unocss/reset': registry.npmmirror.com/@unocss/reset/0.46.5
      '@unocss/transformer-attributify-jsx': registry.npmmirror.com/@unocss/transformer-attributify-jsx/0.46.5
      '@unocss/transformer-compile-class': registry.npmmirror.com/@unocss/transformer-compile-class/0.46.5
      '@unocss/transformer-directives': registry.npmmirror.com/@unocss/transformer-directives/0.46.5
      '@unocss/transformer-variant-group': registry.npmmirror.com/@unocss/transformer-variant-group/0.46.5
      '@unocss/vite': registry.npmmirror.com/@unocss/vite/0.46.5_vite@3.2.3
    transitivePeerDependencies:
      - rollup
      - supports-color
      - vite
    dev: true

  registry.npmmirror.com/upper-case-first/2.0.2:
    resolution: {integrity: sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/upper-case-first/-/upper-case-first-2.0.2.tgz}
    name: upper-case-first
    version: 2.0.2
    dependencies:
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/upper-case/2.0.2:
    resolution: {integrity: sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/upper-case/-/upper-case-2.0.2.tgz}
    name: upper-case
    version: 2.0.2
    dependencies:
      tslib: registry.npmmirror.com/tslib/2.4.1
    dev: true

  registry.npmmirror.com/util-deprecate/1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz}
    name: util-deprecate
    version: 1.0.2
    dev: true

  registry.npmmirror.com/v8flags/4.0.0:
    resolution: {integrity: sha512-83N0OkTbn6gOjJ2awNuzuK4czeGxwEwBoTqlhBZhnp8o0IJ72mXRQKphj/azwRf3acbDJZYZhbOPEJHd884ELg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/v8flags/-/v8flags-4.0.0.tgz}
    name: v8flags
    version: 4.0.0
    engines: {node: '>= 10.13.0'}
    dev: true

  registry.npmmirror.com/vue-virtual-scroll-list/2.3.5:
    resolution: {integrity: sha512-YFK6u5yltqtAOfTBcij/KGAS2SoZvzbNIAf9qTULauPObEp53xj22tDuohrrM2vNkgoD5kejXICIUBt2Q4ZDqQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/vue-virtual-scroll-list/-/vue-virtual-scroll-list-2.3.5.tgz}
    name: vue-virtual-scroll-list
    version: 2.3.5
    dev: false

  registry.npmmirror.com/wcwidth/1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wcwidth/-/wcwidth-1.0.1.tgz}
    name: wcwidth
    version: 1.0.1
    dependencies:
      defaults: registry.npmmirror.com/defaults/1.0.4
    dev: true

  registry.npmmirror.com/which/1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/which/-/which-1.3.1.tgz}
    name: which
    version: 1.3.1
    hasBin: true
    dependencies:
      isexe: registry.npmmirror.com/isexe/2.0.0
    dev: true

  registry.npmmirror.com/which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/which/-/which-2.0.2.tgz}
    name: which
    version: 2.0.2
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: registry.npmmirror.com/isexe/2.0.0
    dev: true

  registry.npmmirror.com/wordwrap/1.0.0:
    resolution: {integrity: sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wordwrap/-/wordwrap-1.0.0.tgz}
    name: wordwrap
    version: 1.0.0
    dev: true

  registry.npmmirror.com/wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz}
    name: wrap-ansi
    version: 7.0.0
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmmirror.com/ansi-styles/4.3.0
      string-width: registry.npmmirror.com/string-width/4.2.3
      strip-ansi: registry.npmmirror.com/strip-ansi/6.0.1
    dev: true

  registry.npmmirror.com/wrappy/1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz}
    name: wrappy
    version: 1.0.2
    dev: true

  registry.npmmirror.com/yocto-queue/0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==, registry: https://registry.npm.taobao.org/, tarball: https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz}
    name: yocto-queue
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true
