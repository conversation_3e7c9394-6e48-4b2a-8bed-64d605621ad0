<template>
  <div class="content">
    <div v-if="!roles.length">
      <el-empty
        class="empty "
        :image-size="100"
        description="暂未查询到数据~"
      />
    </div>
    <template v-if="multiple">
      <div
        v-for="item in roles"
        :key="item.roleId"
        class="w-full mb-10px pl-2px flex cursor-pointer"
        :label="item.roleId"
        @click.stop.prevent="handleCheckboxClick(item)"
      >
        <el-checkbox
          :key="item.roleId"
          :value="checkboxValue(item)"
        />
        <span class="text ml-10px">
          {{ item.roleName }}
        </span>
      </div>
    </template>
    <template v-else>
      <div
        v-for="item in roles"
        :key="item.roleId"
        class="mb-10px"
        @click.stop.prevent="handleRadioClick(item)"
      >
        <el-radio
          :value="radioCheckValue(item)"
          :label="item.roleId"
        >
          <span class="w-full">
            {{ item.roleName }}
          </span>
        </el-radio>
      </div>
    </template>
  </div>
</template>

<script>
import { ROLE } from "./config";
import mixins from "./mixins";

export default {
  mixins: [mixins],
  props: {
    roles: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      radioValue: "",
      checkList: [],
    };
  },
  computed: {
    // 把checkList 转换[]roleItem 存放
    selectDataList() {
      const result = [];
      for (const id of this.checkList) {
        result.push(this.roleObj[id]);
      }
      return result;
    },
    // 先把roles数组转换成对象，到时候 再根据id去查找数据的时候 效率更高
    roleObj() {
      const object = {};
      for (const item of this.roles) {
        object[item.roleId] = item;
      }
      return object;
    },
  },
  watch: {
    radioValue(newValue) {
      this.checkList = !newValue ? [] : [newValue];
      this.$appSelectUserRef.handleItemClick({
        [ROLE]: this.selectDataList,
      });
    },
    [`selectData.${ROLE}`]() {
      this.setCheckList();
    },
  },
  created() {
    this.setCheckList();
  },
  methods: {
    onChange() {
      this.$appSelectUserRef.handleItemClick({
        [ROLE]: this.selectDataList,
      });
    },
    setCheckList() {
      if (this.selectData[ROLE]) {
        this.checkList = this.selectData[ROLE].map((item) => item.roleId);
        if (!this.multiple && this.checkList.length) {
          this.radioValue = this.checkList[0];
        }
      }
    },
    handleCheckboxClick(item) {
      if (this.checkboxValue(item)) {
        this.handleItemDelClick(item);
      } else {
        this.checkList.push(item.roleId);
      }
      this.onChange();
    },
    handleRadioClick(item) {
      this.checkList = [item.roleId];
      this.onChange();
    },
    checkboxValue(item) {
      return this.checkList.includes(item.roleId);
    },
    radioCheckValue() {
      return this.checkList[0]
    },
    // 主要用于 右侧点击删除按钮清空选中状态
    handleItemDelClick(data) {
      this.checkList = this.checkList.filter(
        (roleId) => roleId !== data.roleId
      );
      this.onChange();
    },
    // 清空全部
    handleDeleteAllClick() {
      if (!this.multiple) {
        this.radioValue = "";
      }
      this.checkList = [];
      this.onChange();
    },
  },
};
</script>
<style lang="scss" scoped>
.text {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #6a6f7f;
}
</style>