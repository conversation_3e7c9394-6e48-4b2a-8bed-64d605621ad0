/**
 * @description: 数值转PX，兼容传入 number 或者 string 类型
 * @param { number|number } value 要转换的数值 或者字符串 
 * @return { string } px字符串
 */
export const parse2px = (value)=>{
  const valueIsNumber = typeof value === "number"
  const valueIsString = typeof value === "string"
  if (!valueIsNumber && !value) return ""
  if(!valueIsString && !valueIsNumber) throw new Error("parse2px value 数据格式有问题")
  if(valueIsString && (value.endsWith("%") || value.endsWith("vh"))) return value
  if (valueIsNumber || !value.includes("px")) return `${value}px`
  return value
}

export function debounce(func, delay) {
  let timeoutId;

  return function (...args) {
    // 清除之前设置的定时器
    clearTimeout(timeoutId);

    // 设置新的定时器
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}

// 数组转树形结构 
export const list2Tree = (arr,parentId = null)=> {
  const tree = [];
  arr.forEach(item => {
    item.children = item.children || []
    if (item.parentId === parentId) {
      const children = list2Tree(arr, item.id);
      if (children.length) {
        item.children = children;
      }
      tree.push(item);
    }
  });
  return tree;
}

export function cloneDeep(obj, memo = new WeakMap()) {
  // 如果是基本数据类型或者 null，则直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 如果已经被克隆过，直接返回之前的克隆结果，避免循环引用导致的无限递归
  if (memo.has(obj)) {
    return memo.get(obj);
  }

  // 创建一个新的对象或数组
  const result = Array.isArray(obj) ? [] : {};

  // 将当前对象存储到 memo 中
  memo.set(obj, result);

  // 遍历对象的属性或数组的元素，递归调用 cloneDeep
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result[key] = cloneDeep(obj[key], memo);
    }
  }

  return result;
}

export function isEqual(value, other) {
  // 如果两个值相等（包括处理 NaN）
  if (value === other || (value !== value && other !== other)) {
    return true;
  }

  // 如果两个值不是对象或者有一个为 null
  if (value == null || other == null || typeof value !== 'object' || typeof other !== 'object') {
    return false;
  }

  // 获取对象的属性名
  const keysA = Object.keys(value);
  const keysB = Object.keys(other);

  // 如果属性数量不相等
  if (keysA.length !== keysB.length) {
    return false;
  }

  // 遍历对象的属性进行深度比较
  for (let key of keysA) {
    if (!keysB.includes(key) || !isEqual(value[key], other[key])) {
      return false;
    }
  }

  return true;
}

export function keyBy(collection, iteratee) {
  return collection.reduce((result, item) => {
    const key = typeof iteratee === 'function' ? iteratee(item) : item[iteratee];
    result[key] = item;
    return result;
  }, {});
}

export const noop = ()=>{}