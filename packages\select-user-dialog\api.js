import { post } from "~/utils/fetch"
import { list2Tree } from "~/utils/index"

// 获取用户
export const getListMerchantMemberApi = async(params) => {
  const data = await post("/api/merchant/platform/listMerchantMember", params)
  data.list.map(item=>{
    item.enable = !item.disabled
    return item
  })
  return data
}

// 根据用户ID获取用户
export const getIdsListMerchantMemberApi = ids => getListMerchantMemberApi({
  filters:{
    userId:[...ids],
    withDeleted:true
  }
})

// 获有部门的数据
export const getListDeptApi = (params={}) => post("/api/merchant/platform/listDept", params)

// 根据用户部门ID获取部门信息
export const getIdsListDepApi = ids =>{
  return getListDeptApi({
    filters:{
      id:[...ids]
    }
  })
}


// 根据部门获取用户ID
export const getUserDepListApi = async (params) => {
  try {
    const { list } = await getListDeptApi({
      filters:{
        id:[params.deptId],
        withChildren:true,
        withMember:true
      },
    })
  
    const member = list[0].members.map(item=>{
      item.enable = !item.disabled
      return item
    })

    return member
  }catch(err){
    console.error(err)
    return []
  }
}

// 获取角色列表
export const getListRoleApi = params => post("/api/merchant/platform/listRole", params)

// 根据角色ID获取角色的信息
export const getIdsListRoleApi = ids => {
  return getListRoleApi({
    filters:{
      id:[...ids]
    }
  })
}

/**
 * @description: 获取角色列表
 * 平台接口跟费控的获取角色接口对比 缺少出参字段
 * {
 *  "roleId": 1,
 ***  "roleCode": "SALARY_MANAGER",
 *  "roleName": "薪酬岗",
 ***  "enabled": true,
 ***  "roleType": 1
 *},
 */
export const geRoleListApi = async (params) => {

  const { list } = await getListRoleApi({
    filters:{
    }
  })
  // return get("/api/expense/role/list", params)
  return list.map(item=>{
    item.roleId = item.id
    item.roleName = item.name
    return item
  })
}


// 获取部门 ， 树形结构
export const getDepartmentTreeApi = async (params) => {
  // 获取所有部门
  const { list } = await getListDeptApi({
    filters:{}
  })
  list.forEach(item=>{
    item.descendantMemberCount = item.descendantMemberNum
    item.memberCount = item.memberNum
  })
  const treeData = list2Tree(list)

  treeData[0].children.forEach(item=>{
    item._isRoot = true
  })

  return treeData[0].children
}

