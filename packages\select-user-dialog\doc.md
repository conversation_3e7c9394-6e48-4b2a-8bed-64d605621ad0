## z-select-user-dialog 选人弹框

适用于费控系统 选择人员/角色/部门

### 基本用法

默认展示 指定用户/指定部门/指定角色 筛选项 ， 控制 v-model 可以设置选人弹框的显示隐藏。
用户点击弹框确定以后，通过 confirm 绑定的回调函数，可以拿到用户选择的数据，然后自行实现业务逻辑。

```html
<z-select-user-dialog @confirm="confirm" v-model="show" />
<el-button @click="show=true">打开弹框</el-button>

<script>
  export default {
    data() {
      return {
        show: false,
      }
    },
    methods: {
      // 这里可以拿到 选中的数据
      confirm({ user, dept, role }) {
        console.log({ user, dept, role })
      },
    },
  }
</script>
```

### 指定筛选项

只要给元素设置 `tabField` 属性 ， 可以控制当前展示的筛选项，他接收一个字符串。可以输入多个以逗号分割。
user=指定用户，dept=指定部门，role=指定角色。

```html
<z-select-user-dialog @confirm="confirm" tabField="user" v-model="show" />
<el-button @click="show=true">打开选人弹框</el-button>

<z-select-user-dialog @confirm="confirm" tabField="dept" v-model="show1" />
<el-button @click="show1=true">打开选部门弹框</el-button>

<z-select-user-dialog @confirm="confirm" tabField="role" v-model="show2" />
<el-button @click="show2=true">打开选角色弹框</el-button>

<z-select-user-dialog
  v-model="show3"
  tabField="user,dept"
  @confirm="confirm"
/>
<el-button @click="show3=true">打开选/用户/部门弹框</el-button>

<script>
  export default {
    data() {
      return {
        show: false,
        show1: false,
        show2: false,
        show3: false,
      }
    },
    methods: {
      confirm({ user, dept, role }) {
        console.log({ user, dept, role })
      },
    },
  }
</script>
```

### 设置回显默认值

可以通过调用组件的 setValue 方法可以设置 回显的值。

```html
<z-select-user-dialog ref="select-user-dialog" @confirm="confirm" v-model="show" />
<el-button @click="show=true">打开弹框</el-button>
<el-button @click="handleClick">回显数据</el-button>

<script>
  export default {
    data() {
      return {
        show: false,
      }
    },
    methods: {
      confirm({ user, dept, role }) {
        console.log("选择回调")
      },
      handleClick(){
        this.$refs["select-user-dialog"].setValue({
          roleId:[133], userId:[102723,114115], deptId:[139302]
        })
      }
    },
  }
</script>
```

### 设置为单选状态

通过设置元素 `multiple` 属性，可以设置为单选或者多选状态.

```html
<z-select-user-dialog @confirm="confirm" v-model="show" :multiple="false" />
<el-button @click="show=true">打开弹框</el-button>

<script>
  export default {
    data() {
      return {
        show: false,
      }
    },
    methods: {
      // 这里可以拿到 选中的数据
      confirm({ user, dept, role }) {
        console.log({ user, dept, role })
      },
    },
  }
</script>
```

### 使用内置自定义的选择框， 类似 input

通过设置元素 `show-custom-select` 属性，可以展示自定义的选择框。

```html
<z-select-user-dialog @confirm="confirm" show-custom-select />

<script>
  export default {
    methods: {
      // 这里可以拿到 选中的数据
      confirm({ user, dept, role }) {
        console.log({ user, dept, role })
      },
    },
  }
</script>
```

### Props

| 参数               | 说明                                   | 类型            | 可选值 | 默认值               |
| ------------------ | -------------------------------------- | --------------- | ------ | -------------------- |
| v-model            | 是否显示隐藏                           | boolean         | -      | -                    |
| multiple           | 是否多选                               | boolean         | false  | true                 |
| title              | 弹框标题                               | String          | -      | 请选择范围           |
| scene              | 使用场景                               | String          | search | -                    |
| modal              | 是否展示遮罩层                         | boolean         | false  | true                 |
| require            | 是否必填，如果必填点击确定至少选择一项 | boolean         | -      | true                 |
| tabActive          | 顶部筛选默认高亮的 key                 | dept,role | -      | 'user'               |
| tabField           | 筛选默认展示的选项，以逗号分割         | string          | -      | user,dept,role |
| data               | 默认选中的数据                         | Object          | -      | -                    |
| limit              | 限制选择最大数量                       | Number          | -      | 999999               |
| placeholder        | 占位符                                 | String          | -      | 请选择               |
| show-custom-select | 是否展示自定义的输入框                 | boolean         | true   | false                |

### Events

| 参数    | 说明           | 回调参数                   |
| ------- | -------------- | -------------------------- |
| confirm | 点击确定的回调 | ({user, dept, role}) |

### Methods

| 方法名     | 说明         | 参数 |
| ---------- | ------------ | ---- |
| clearField | 清空全部字段 | -    |
