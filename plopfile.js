import { readFileSync, writeFileSync } from "fs"
import { join } from "path"



export default function (plop) {
  plop.setGenerator("app-components", {
    description: "创建全局组件模板",
    prompts: [
      {
        type: "list",
        name: "componentType",
        message: "您要创建的组件类型",
        choices: ["views","business"],
      },
      {
        type: "input",
        name: "name",
        message: "请输入要创建的组件名称",
        validate(value) {
          if (!value.length) return new Error("组件名称不能为空")
          return true
        }
      },
      {
        type: "input",
        name: "cName",
        message: "组件中文名(十个字以内)：",
        validate(value) {
          const pass = value && value.length <= 10
          if (pass) return true
          return "不能为空，且不能超过十个字符"
        },
      },
      {
        type: "input",
        name: "desc",
        message: "组件描述(五十个字以内)：",
      },
      {
        type: "input",
        name: "author",
        message: "组件作者:",
        validate(value) {
          if (value) return true
          return "请把您的姓名或昵称留下！"
        },
      },
    ],
    actions: [
      {
        type: "add",
        path: "packages/{{name}}/doc.md",
        templateFile: "script/componentTemplate/doc.md.hbs"
      },
      {
        type: "add",
        path: "packages/{{name}}/index.scss",
        templateFile: "script/componentTemplate/index.scss.hbs"
      },
      {
        type: "add",
        path: "packages/{{name}}/index.vue",
        templateFile: "script/componentTemplate/index.vue.hbs"
      },
      {
        type: "add",
        path: "examples/src/views/demo/{{name}}/index.vue",
        templateFile: "script/componentTemplateDemo/index.vue.hbs"
      },
      // 把文件写入到路由
      async (res) => {
        
        const getRouterPath = moduleName => join(process.env.PWD, "./examples/src/router/modules/" + moduleName + ".js")

        const routerModulePath = getRouterPath(res.componentType)

        const router = readFileSync(routerModulePath, "utf-8")

        const newRouter = router.replace(/(.*)]/, `
          $1
          {
              path: "/${res.name}",
              component: () => import("@examplesView/demo/${res.name}/index.vue"),
              meta: {
                title: "${res.name}",
              }
            },
          ]
        `)

        try {
          writeFileSync(routerModulePath, newRouter)
          console.log('文件写入成功！，请重启程序并查看运行效果')
        } catch (err) {
          if (err) return console.log('写入文件失败！' + err.message)   // 如果文件写入失败，则报错
        }
      }
    ],
  })
}

