<template>
  <div class="zui-select-user-dialog">
    <z-dialog
      v-model="show"
      width="640px"
      :title="title"
      :modalAppendToBody="modalAppendToBody"
      :modal="modal"
      v-if="isInit"
      :before-close="onBeforeCloseDialog"
      @confirm="confirm"
    >
      <div class="content flex">
        <index-left
          ref="index-left"
          class="content-left flex-1 w-[100px]"
        />
        <index-right class="content-right flex-1 pl-16px pt-16px " />
      </div>
    </z-dialog>
    <index-custom-select
      v-if="input"
      :style="{
        width:_inputWidth
      }"
      ref="index-custom-select"
      :selectData="selectData"
    />
  </div>
</template>
<script>
import { getIdsListDepApi, getIdsListMerchantMemberApi, getIdsListRoleApi } from './api';
import indexCustomSelect from "./index-custom-select.vue";
import { DEPARTMENT, USER, ROLE } from "./config";
import indexRight from "./index-right.vue";
import indexLeft from "./index-left.vue";
import { showMessage } from "~/utils/toast";
import props from "./props";
import { parse2px } from  "~/utils/index"
import { cloneDeep,isEqual } from "~/utils/index"


export default {
  components: {
    indexLeft,
    indexRight,
    indexCustomSelect,
  },
  provide() {
    return {
      parentProps: this.$props,
      selectData: this.selectData,
      $appSelectUserRef: this,
    };
  },
  props,
  computed:{
    _inputWidth(){
      return parse2px(this.inputWidth)
    }
  },
  data() {
    return {
      componentName: "select-user-dialog",
      keyword: "",
      show: false,
      isInit:true,
      selectData: {
        [USER]: [],
        [DEPARTMENT]: [],
        [ROLE]: [],
      },
      oldSelectData: [],
      // 单独设置禁用的数据
      disableds: [],
      tabActive:this.active,
    };
  },
  watch: {
    value(newValue) {
      this.show = newValue;
    },
    show(newValue) {
      if (newValue) {
        this.oldSelectData = cloneDeep(this.selectData);
      } else {
        if (this.showClearSearch) this.$refs["index-left"].clearKeyword();
        if(this.reInit) this.resetState()
        this.$emit("close")
      }
      this.$emit("input", newValue);
    },
    data: {
      handler() {
        this.setSelectData(cloneDeep(this.data));
        this.setCustomSelectData(cloneDeep(this.data));
      },
      deep: true,
    },
  },
  created() {
    this.show = this.value
    this.setDefaultTabActive()
    this.setSelectData(cloneDeep(this.data));
  },
  methods:{
    async resetState(){
      this.clearField()
      this.isInit = false
      await this.$nextTick()
      this.isInit = true
    },
    setDefaultTabActive(){
      if(!this.tabField.includes(",")) {
        this.tabActive = this.tabField
      }
    },
    // 清空全部选择的字段
    clearField() {
      const cmtNames = "index-left-role-list,index-left-tree,index-left-user";
      for (const cmtName of cmtNames.split(",")) {
        this.childIndexLeftRef(cmtName, "handleDeleteAllClick");
      }
    },
    // 这里的数据 是由 指定部门，指定用户，指定角色 点击所触发的回调
    handleItemClick(data) {
      for (const key in data) {
        if (data[key]) {
          this.$set(this.selectData, key, data[key]);
        }
      }
    },
    updateTabActive(value){
      this.tabActive = value
    },
    // 找到子组件的引用 并出发相对应的方法
    childIndexLeftRef(componentName, methodsName, ...parameters) {
      const getLeftcmtReference = this.$refs["index-left"];
      if (getLeftcmtReference) {
        const childCmt = getLeftcmtReference.getRef(componentName);
        childCmt && childCmt[methodsName](...parameters);
      }
    },
    setSelectData(data) {
      Object.assign(this.selectData, data);
      const indexCustomSelectReference = this.$refs["index-custom-select"];
      indexCustomSelectReference && indexCustomSelectReference.setNewSelectData(this.selectData);
    },
    // 关闭弹框前 对比前后数据
    onBeforeCloseDialog(done){
      if (!isEqual(this.oldSelectData, this.selectData)) {
        this.setSelectData(this.oldSelectData);
      }
      done()
    },
    confirm() {
      let isLength = false;
      for (const key of Object.keys(this.selectData)) {
        if (this.selectData[key].length) {
          isLength = true;
          break;
        }
      }

      if (!isLength && this.require && !this.input) {
        return showMessage("请至少选择一条数据", "error");
      }

      const cloneDeepSelectData = cloneDeep(this.selectData);
      this.setCustomSelectData(cloneDeepSelectData);

      this.show = false;
      this.$emit("confirm", cloneDeepSelectData, this.extraData);
    },
    setCustomSelectData(cloneDeepSelectData) {
      const customSelectReference = this.$refs["index-custom-select"];
      customSelectReference && customSelectReference.confirm(cloneDeepSelectData);
    },

    async setValue(data,triggerConfirm=true){
      if(typeof data !== "object") {
        throw "传入的数据格式有误。请传入{roleId:[133], userId:[102723,114115], deptId:[139302]}"
      }

      const defData = Object.assign({
        userId:[],
        roleId:[],
        deptId:[],
      },data)

      const apiMap = {
        userId:getIdsListMerchantMemberApi,
        roleId:getIdsListRoleApi,
        deptId:getIdsListDepApi,
      }

      // 设置ID 已删除
      const setDeleteData = (result,{ userId,deptId,roleId }) => {

        const [user,role,dept] = result
        if(user.list.length !== userId.length) {
          let resultUserIds = user.list.map(item=>item.userId)

          userId.forEach(id=>{
            if(!resultUserIds.includes(id)) {
              user.list.push({
                userId:id,
                name:`(${id})已删除`
              })
            }
          })
        }

        if(dept.list.length !== deptId.length) {
          let resultDeptIds = dept.list.map(item=>item.id)

          deptId.forEach(id=>{
            if(!resultDeptIds.includes(id)) {
              dept.list.push({
                id:id,
                name:`(${id})已删除`
              })
            }
          })
        }

        if(role.list.length !== roleId.length) {
          let resultRoleIds = role.list.map(item=>item.id)

          roleId.forEach(id=>{
            if(!resultRoleIds.includes(id)) {
              role.list.push({
                id:id,
                name:`(${id})已删除`
              })
            }
          })
        }
        
        return result
      }

      const apis = Object.keys(defData).map(key=>{
        const ids = defData[key]
        if(!ids.length) {
          return {
            list:[]
          }
        }
        return apiMap[key](data[key]);
      })

      const [ user,role,dept ] = setDeleteData(await Promise.all(apis),defData)

      this.setSelectData({
        user:user.list,
        role:role.list,
        dept:dept.list,
      })

      triggerConfirm && this.confirm()
    }
  }
};
</script>
<style lang="scss">
@import "./index.scss";
</style>
