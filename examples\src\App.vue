<template>
  <el-container class="examples-container h-100vh">
    <el-aside
      width="200px"
      class="bg-gray-500 "
    >
      <nav-menu />
    </el-aside>

    <el-container>
      <el-header class="el-header flex items-center text-8 bg-gray-400">
        <span class="text-white">olading-zui</span>
      </el-header>

      <el-main>
        <router-view />
      </el-main>
    </el-container>

  </el-container>

</template>
<script>
import navMenu from "./components/nav-menu/index.vue";
export default {
  components: {
    navMenu,
  },
};
</script>
<style lang="scss">
.examples-container {
  .el-menu {
    border: 0;
  }
}
</style>
