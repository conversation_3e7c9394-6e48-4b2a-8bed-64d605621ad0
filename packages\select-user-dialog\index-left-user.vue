<template>
  <div class="index-left-user-container flex flex-col h-full">
    <div v-if="!options.length">
      <el-empty
        class="empty "
        :image-size="100"
        description="暂未查询到数据~"
      />
    </div>
    <div v-else>
      <ul
        v-show="!keyword"
        class="menus flex pb-4px"
      >
        <li
          v-for="(item,index) in menus"
          :key="item.id"
          @click="handleMenusClick(item,index)"
        >
          {{ item.name }}
        </li>
      </ul>
      <ul class="tree ">
        <div
          class="user-all-checkbox flex items-center py-4px"
          @click.stop.prevent="handleAllSelectClick"
          v-show="!keyword"
        >
          <el-checkbox
            v-if="showAllSelectBtn "
            :value="isAllSelect"
            :disabled="selectAllIsDisabled"
          >
            <span class="relative top-1px block">全选本级部门人员</span>
          </el-checkbox>
        </div>

        <el-empty
          v-show="!mergeTreeOptions.length"
          class="empty "
          :image-size="100"
          description="当前部门下暂无查询到人员数据哦~"
        />
        <virtual-list
          v-if="!keyword"
          :style="{
            height:virtualHeight
          }"
          :data-key="'id'"
          class="pr-20px zui__scrollbar-y"
          :data-sources="mergeTreeOptions"
          :data-component="itemComponent"
        />

        <!-- 人员搜索列表 -->
        <template v-if="keyword">
          <li
            v-for="(item) in newUserList"
            v-show="newUserList.length"
            :key="item.userId"
            class="flex person "
            @click.stop.prevent="handlePersonClick(item)"
          >
            <div class="flex items-center">
              <!-- 用户设置了多选 -->
              <template v-if="multiple">
                <el-checkbox
                  :value="selectCheckbox(item)"
                  :disabled="isDisabled(item)"
                >
                  <span />
                </el-checkbox>
              </template>

              <!-- 启用单选状态按钮 -->
              <template v-else>
                <!-- {{disabled(item)}} -->
                <!-- {{$appSelectUserRef.scene}} -->
                <el-radio
                  :value="selectCheckbox(item)"
                  :label="item.userId"
                  :disabled="isDisabled(item)"
                >
                  <span />
                </el-radio>
              </template>
              <div class="oi-icon_single olading-iconfont text-15px text-gray-400 mr-4px"></div>
              <span class="flex-1">{{ item.name }} </span>
            </div>
          </li>
        </template>
      </ul>
      <el-empty
        v-if="!newUserList.length && keyword"
        class="empty"
        :image-size="100"
        description="暂无查询到人员数据~"
      />
    </div>
  </div>
</template>

<script>
import indexLeftUserItem from "./index-left-user-item.vue";
import VirtualList from "vue-virtual-scroll-list";
import { getUserDepListApi } from "./api";
import { showMessage } from "~/utils/toast";
import { USER } from "./config";
import mixins from "./mixins";
import { cloneDeep,keyBy } from "~/utils/index"


const toMapIds = (list) => {
  const map = {};
  for (const item of list) {
    map[item.userId] = item;
  }
  return map;
};

export default {
  components: {
    VirtualList,
  },
  mixins: [mixins],
  inject: ["showLeftTitle"],
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    keyword: {
      type: String,
      default: "",
    },
    userList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      menusActive: [],
      featUserList: [],
      // 储存选中的数据
      selectPerson: [],
      itemComponent: indexLeftUserItem,
      cacheUserList: {},
      virtualStyle: {
        height: "200px",
      },
    };
  },
  computed: {
    virtualHeight(){
      if(this.parentProps.tabField === "user") {
        return "276px"
      }
      return "190px"
    },
    selectAllIsDisabled() {
      return !this.newUserList.some((item) => !item.disabled);
    },
    // 是否展示全选按钮
    showAllSelectBtn() {
      if (this.$appSelectUserRef.scene === "search") return false;
      // 如果当前部门下的人员 全部是禁用状态 不必再显示权限按钮
      if (this.newUserList.filter((item) => !this.isDisabled(item)).length === 0) return false;
      return !this.keyword && this.multiple;
    },
    // 是否全选状态
    isAllSelect() {

      // 当前展开树下的所有员工
      const currentTreeAllIdMap = toMapIds(this.newUserList.filter(item=>!this.isDisabled(item)));

      // 当前已经选中的员工集合
      const currentSelectIdMap = toMapIds(this.selectPerson);

      // 如果当前选择的人数为空 ， 直接取消所有层级的全选
      if (!Object.keys(currentSelectIdMap).length) return false;

      // 如果当前 树下面的人员ID 如果不存在已选中的人员map里边 就代表不是全选
      for(let uId of Object.keys(currentTreeAllIdMap)) {
        if(!currentSelectIdMap[uId]) return false
      }

      return true

    },
    newUserList() {
      const maxLength = 50;

      const disabledMap = keyBy(this.$appSelectUserRef.disableds);

      for (const item of (this.featUserList || [])) {
        if (disabledMap[item.userId]) this.$set(item, "disabled", true);
      }

      if (this.featUserList.length && !this.keyword) return this.featUserList;

      if (!this.keyword) return [];

      const result = this.userList.filter((item) => {
        if (disabledMap[item.userId]) this.$set(item, "disabled", true);
        return item.name;
      });
      result.splice(maxLength);
      return result;
    },
    newOptions() {
      const result = cloneDeep(this.options);

      // 递归判断是否为根节点，  根节点不允许展开展开， 并生成一个新的数据
      const setLeaf = (item) => {
        item.leaf = false;
        if (!item.children.length) {
          item.leaf = true;
        } else {
          for (const it of item.children) {
            setLeaf(it);
          }
        }
      };
      for (const item of result) {
        setLeaf(item);
      }

      return result;
    },
    menus() {
      const result = [];
      let data = this.newOptions;
      for (const item of this.menusActive) {
        data = data.children ? data.children[item] : data[item];
        result.push(data);
      }

      return result;
    },
    mergeTreeOptions() {
      if (!this.treeOptions) return [];

      const treeOptions = cloneDeep(this.treeOptions.children);

      for (const item of this.newUserList) {
        item.id = item.userId;
        treeOptions.push(item);
      }

      return treeOptions;
    },
    treeOptions() {
      return this.menus[this.menus.length - 1];
    },
  },
  watch: {
    [`selectData.${USER}`]() {
      this.setSelectPerson();
    },
    options() {
      if (this.menusActive.length === 0) {
        this.handleItemClick(this.newOptions[0], 0);
      }
    },
  },
  created() {
    this.setSelectPerson();
  },
  methods: {
    setSelectPerson() {
      if (this.selectData[USER]) {
        this.selectPerson = this.selectData[USER];
      }
    },
    isDisabled(item) {
      if (this.$appSelectUserRef.scene === "search") return false;
      return !item.enable || item.disabled;
    },
    // 点击部门
    async handleItemClick(item, index) {
      
      this.menusActive.push(index);
      const { beforeUserApiRequest } = this.$appSelectUserRef
 
      // 如果当前选中的是根节点了 ， 在进行人员的获取
      this.$parent.isLoading = true
      try {
        const deptId = item.id;
        this.featUserList = [];
        this.featUserList = await getUserDepListApi({
          deptId,
        });

        if(typeof beforeUserApiRequest === 'function') {
          const data = await beforeUserApiRequest(this.featUserList)
          console.log('data')
          if (data) {
            this.featUserList = data
          }
        }
        
        this.cacheUserList[deptId] = this.featUserList;
      } finally {
        this.$parent.isLoading = false;
      }
    },
    // 点击选择人员
    handlePersonClick(item) {
      // 非启用状态不可点击
      if (
        item.hasOwnProperty("enable") &&
        !item.enable &&
        !this.$appSelectUserRef.scene
      )
        return;

      if (this.isDisabled(item)) return;

      // 如果当前是多选状态
      if (this.multiple) {
        if (this.selectPerson.some((it) => it.userId === item.userId)) {
          this.selectPerson = this.selectPerson.filter(
            (it) => it.userId !== item.userId
          );
        } else {
          if (this.selectPerson.length >= this.limit) {
            return showMessage(`最多只能选择${this.limit}人`, "error");
          }
          this.selectPerson.push({ ...item });
        }
      } else {
        if (!this.selectPerson.length) {
          this.selectPerson = [item];
        } else {
          const selectPersonItem = this.selectPerson[0];
          this.selectPerson =
            selectPersonItem.userId === item.userId && selectPersonItem
              ? []
              : [item];
        }
      }

      this.$appSelectUserRef.handleItemClick({
        user: this.selectPerson,
      });
    },
    // 点击导航 ， 回头上级目录 ， 只需要把 menusActive 数据保存的点击目录索引 ， 想当前点击tab的索引 -1 即可
    handleMenusClick(_item, index) {
      this.featUserList = this.cacheUserList[_item.id]
        ? this.cacheUserList[_item.id]
        : [];

      this.menusActive.splice(index + 1);
    },
    // 选择框组件 是否选中状态
    selectCheckbox(data) {
      // 多选
      if (this.multiple) {
        return this.selectPerson.some((item) => item.userId === data.userId);
      }
      // 单选
      return this.selectPerson[0] && this.selectPerson[0].userId;
    },
    // 清空全部
    handleDeleteAllClick() {
      this.selectPerson = [];
      this.$appSelectUserRef.handleItemClick({
        user: this.selectPerson,
      });
    },
    // 点击全选
    handleAllSelectClick() {
      // 如果是全选的状态 ， 那就要取消选中状态
      if (this.isAllSelect) {
        const userIds = new Set(this.newUserList.map((item) => item.userId));

        this.selectPerson = this.selectPerson.filter((item) => {
          if (item.disabled) return true;
          return !userIds.has(item.userId);
        });

        this.$appSelectUserRef.handleItemClick({
          user: this.selectPerson,
        });
      } else {
        // 找到当前选中的 用户id集合
        const userIds = new Set(this.selectPerson.map((item) => item.userId));

        // 遍历当前 列表下的所有用户ID
        const pushList = this.newUserList.filter((item) => {
          // 当前列表下的用户ID，没有存在选中集合中，则向选中集合插入数据 , 并且用户状态都是启用的
          return !userIds.has(item.userId) && item.enable;
        });

        if (pushList.length + this.selectPerson.length >= this.limit) {
          return showMessage(`最多只能选择${this.limit}人`, "error");
        }

        for (const item of pushList) this.selectPerson.push(item);
      }
    },
  },
};
</script>

<style lang="scss">
.index-left-user-container {
  .el-radio {
    margin-right: 0 !important;
    height: 14px;
  }
  .member-count {
    color: #999;
    flex-shrink: 0;
  }

  .menus {
    font-size: 12px;
    color: #6a6f7f;
    flex-wrap: wrap;

    li {
      cursor: pointer;
      &:after {
        content: "\E6E0";
        display: inline-block;
        margin: 0 4px;
        width: 10px;
        height: 10px;
        font-family: element-icons !important;
      }
      &:last-child {
        &:after {
          content: none;
        }
      }
    }
  }
  .tree {
    flex: 1;
    overflow: hidden;
    font-size: 14px;
    color: #070f29;
    position: relative;
    .empty {
      position: absolute;
      left: 0;
      width: 100%;
      top: 0;
    }
    .icon {
      background-size: cover;
      width: 16px;
      height: 16px;
      display: block;
      margin-right: 6px;
      flex-shrink: 0;
    }
    li {
      cursor: pointer;
      align-items: center;
      padding: 10px 0;
    }
  }
  .next {
    color: rgb(158, 163, 186);
    flex-shrink: 0;
    &::before {
      content: "|";
      margin: 0 6px;
    }
  }
}
</style>