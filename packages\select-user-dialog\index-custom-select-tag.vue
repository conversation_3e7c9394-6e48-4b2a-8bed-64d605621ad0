<script>
import mixins from "./mixins"
export default {
  props:["item", "field"],
  mixins: [mixins],
  render (createElement) {
    const { name, names, roleName } = this.item

    return createElement("el-tag", {
      props:{
        closable:true,
        size:"small",
        type:"info",
        closable:!this.disabled,
        disableTransitions:true
      },
      class:"tag",
      on:{
        close :() => {
          this.$parent.handleTagDelClick(this.item, this.field)
        }
      }
    }, [
      createElement("span",  {
        attrs: {
          class:"max-width truncate",
          title:name || names || roleName
        },
      },name || names || roleName)
    ])
  }
}
</script>
<style lang="scss" scoped>
.tag{
  margin-right: 5px;
  display:flex;
  align-items: center;
  ::v-deep .el-icon-close{
    top:0;
  }
}
.max-width{
  max-width: 150px;
}
</style>