/*
 * @Date: 2022-03-09 16:57:42
 * @LastEditors: zhaoxm
 * @LastEditTime: 2023-12-28 15:40:21
 * @Description:props 选人弹框
 */

import { DEPARTMENT, USER, ROLE } from "./config"

export default {
  showClearSearch:{
    type: Boolean,
    default: true,
  },
  beforeUserApiRequest:{
    type: Function,
    default: ()=>{}
  },
  // 每次打开弹框 自动调用接口重新请求
  refreshMember:{
    type: Boolean,
    default: false,
  },
  // 每次打开 刷新全部状态
  reInit:{
    type: Boolean,
    default: false,
  },
  // 显示隐藏弹框
  value: {
    type: Boolean,
    default: false,
    require: true,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "请选择范围",
  },
  // 使用场景
  scene:{
    type: String,
    default: "",
  },
  // tab默认展示切换的选项，以逗号分割，  user：指定用户  ， dept：指定部门， role：指定角色
  tabField: {
    type: String,
    default: `${USER},${DEPARTMENT},${ROLE}`,
    require: true,
  },
  // 额外参数 ， 用于带给 confirm
  extraData: {
    type: Object,
    default: () => {},
  },
  // tab默认选项选中的数据
  active: {
    type: String,
    default: USER,
  },
  // dialog 是否展示遮罩层
  modal: {
    type: Boolean,
    default: true,
  },
  modalAppendToBody:{
    type: Boolean,
    default: true,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true,
  },
  // 选择最大数量
  limit: {
    type: Number,
    default: 99_999,
  },
  // 是否必填 ， 如果必填 则必须至少选择一条数据
  require: {
    type: Boolean,
    default: true,
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
  // 是否展示自定义的输入框
  input: {
    type: Boolean,
    default: false,
  },
  inputWidth:{
    type:[Number,String],
    default:"100%"
  },
  // 数据是否缓存， 默认开启， 选中部门或者人员 第一次加载完数据以后， 再次切换筛选项 不会请求接口获取数据
  // 缓存失效是跟随当前页面组件的生命周期 ， 当前组件销毁缓存也会跟随销毁
  cache: {
    type: Boolean,
    default: false,
  },
  // tree 组件的 props 树形
  treeProps: {
    type: Object,
    default: () => {
      return {
        label: "name",
        children: "children",
        isLeaf: "leaf",
      }
    },
  },
  data: {
    type: Object,
    default: () => {
      return {
        [USER]: [],
        [DEPARTMENT]: [],
        [ROLE]: [],
      }
    },
  },
}
