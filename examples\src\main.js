import Vue from "vue"
import oladingUI from "~/src/index.js"
import App from "./App.vue"
import router from "./router"

import '@unocss/reset/tailwind.css'
import ElementUI from "element-ui"
import "./assets/style/element.ui.css"
import "./assets/iconfont/iconfont.css"
import { envConfig } from "./config/env"

(()=> {
  const body = document.querySelectorAll("body")[0]
  body.style.setProperty("--o-primary-color",envConfig.theme.primary)
  body.style.setProperty("--o-primary-color-hover",envConfig.theme.primaryHover )
})()

oladingUI.setConfig(envConfig)

Vue.use(oladingUI)
Vue.use(ElementUI)

new Vue({
  router,
  render: h => h(App),
}).$mount("#app")
