
<template>
  <div
    class="content"
    style="padding-right:20px;"
  >
    <div v-if="!options.length">
      <el-empty
        class="empty "
        :image-size="100"
        description="暂未查询到数据~"
      />
    </div>

    <el-tree
      v-else
      ref="tree"
      :data="newOptions"
      :props="treeProps"
      class="overflow-y-auto"
      :filter-node-method="filterNode"
      default-expand-all
      node-key="id"
    >
      <div
        slot-scope="{ node, data }"
        class="flex"
         @click.stop.prevent="handleCheckClick(data)"
      >
        <div>
          <template v-if="multiple">
            <el-checkbox :value="selectCheckbox(data)">
              <span />
            </el-checkbox>
          </template>
          <template v-else>
            <el-radio
              v-if="isRadio"
              :value="selectCheckbox(data)"
              :label="data.id"
            >
              <span />
            </el-radio>
          </template>
        </div>
        <span style="padding-right:20px;">{{ node.label }}</span>
      </div>
    </el-tree>
  </div>
</template>

<script>
import { DEPARTMENT } from "./config"
import mixins from "./mixins"
import { cloneDeep } from "~/utils/index"

export default {
  mixins: [mixins],
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    keyword: {
      type: String,
      default: "",
    },
  },
  data () {
    return {
      treeProps: this.parentProps.treeProps,
      selectDataList: [],
      isRadio:true,
    }
  },
  computed: {
    newOptions () {
      const result = cloneDeep(this.options)

      // 递归判断是否为根节点，  根节点不允许展开展开， 并生成一个新的数据
      const setLeaf = (item) => {
        if (!item.children.length) {
          item.leaf = true
        } else {
          for (const it of item.children) {
            setLeaf(it)
          }
        }
      }
      for (const item of result) {
        setLeaf(item)
      }

      return result
    },
  },
  watch: {
    keyword (value) {
      const treeRef = this.$refs.tree
      treeRef && treeRef.filter(value || "")
    },
    [`selectData.${DEPARTMENT}`] () {
      this.setSelectDataList()
    },
  },
  created () {
    this.setSelectDataList()
  },
  methods: {
    setSelectDataList () {
      if (this.selectData[DEPARTMENT]) {
        this.selectDataList = this.selectData[DEPARTMENT]
      }
    },
    handleCheckClick (item) {
      // 如果当前是多选状态
      if (this.multiple) {
        if (this.selectDataList.some((it) => it.id === item.id)) {
          this.selectDataList = this.selectDataList.filter(
            (it) => it.id !== item.id
          )
        } else {
          this.selectDataList.push({ ...item })
        }
      } else {
        this.selectDataList = [{ ...item }]
      }
    
      this.$appSelectUserRef.handleItemClick({
        [DEPARTMENT]: this.selectDataList,
      })
    },
    handleItemDelClick(item){
      // 如果当前是多选状态
      if (this.multiple) {
        if (this.selectDataList.some((it) => it.id === item.id)) {
          this.selectDataList = this.selectDataList.filter(
            (it) => it.id !== item.id
          )
        }
      } else {
        this.selectDataList = this.selectDataList.filter(it=>it.id!==item.id)
      }
    
      this.$appSelectUserRef.handleItemClick({
        [DEPARTMENT]: this.selectDataList,
      })
    },
    // 设置多选框是否选中
    selectCheckbox (data) {
      const hasId = this.selectDataList.some((item) => item.id === data.id)
      if (!this.multiple) return hasId ? data.id : ""
      return hasId
    },
    // 关键字搜索，树进行过滤
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 清空全部
    handleDeleteAllClick () {
      this.selectDataList = []
      this.$appSelectUserRef.handleItemClick({
        [DEPARTMENT]: this.selectDataList,
      })
    },
  },
}
</script>
<style lang="scss">
.content {
  .el-tree-node {
    > .el-tree-node__children {
      overflow: visible;
    }
  }
  .comp-tree .tree-body .body-tree .custom-tree-node {
    overflow-x: visible !important;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content,
  .el-tree-node:focus > .el-tree-node__content,
  .el-tree-node__content:hover {
    background: none !important;
  }
  .el-tree-node__content {
    padding-right: 20px;
  }
}
</style>