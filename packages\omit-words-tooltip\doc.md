## z-omit-words-tooltip 省略文字提示

用于文字过长 hover 弹出 tooltip 展示省略的完整文字内容

### 基本用法

你可以把 `z-omit-words-tooltip` 当做一个普通的元素，利用 css 设置元素的宽度，超出长度会自动隐藏。  
当鼠标移入该元素 会自动弹出 tooltip。

```html
<template>
  <z-omit-words-tooltip :content="content"
    >{{content}}</z-omit-words-tooltip
  >
</template>

<script>
  export default {
    data() {
      return {
        content:
          "萨达收到阿斯顿阿斯顿阿斯顿阿斯顿阿斯顿阿斯顿阿斯顿阿斯顿ads阿斯顿阿斯顿阿斯顿",
      }
    },
  }
</script>
```

### Props

| 参数      | 说明                      | 类型   | 可选值 | 默认值                                                                                                    |
| --------- | ------------------------- | ------ | ------ | --------------------------------------------------------------------------------------------------------- | ------ | --- |
| content   | 文字过长 hover 显示的内容 | String | -      | -                                                                                                         |
| tag       | 元素标签                  | string | -      | span                                                                                                      |
| placement | tooltip 的出现位置        | String | -      | top/top-start/top-end/bottom/bottom-start/bottom-end/left/left-start/left-end/right/right-start/right-end | bottom |     |
