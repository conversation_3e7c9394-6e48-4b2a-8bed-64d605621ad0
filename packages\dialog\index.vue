<!--
 * @Date: 2022-03-27 23:38:03
 * @LastEditors: zhaoxm
 * @LastEditTime: 2023-12-26 16:23:10
 * @Description: 弹框
-->

<template>
  <el-dialog
    class="zui-dialog"
    :visible.sync="dialogVisible"
    :modal="modal"
    :closeOnClickModal="closeOnClickModal"
    :closeOnPressEscape="closeOnPressEscape"
    :width="width"
    v-bind="$attrs"
    :show-close="false"
    v-on="$listeners"
  >

    <header class="title" slot="title">
      <span>{{title}}</span>
      <div @click="handleCloseIconClick" style="padding:0;" class="close w-48px h-48px absolute right-0 top-0 flex items-center justify-center" >
        <i v-if="showClose" class="olading-iconfont oi-shanchu" />
      </div>
    </header>

    <main>
      <slot />
    </main>

    <footer
      v-if="showFootBtn"
      slot="footer"
      class="dialog-footer def_per_justifyContentEnd"
    >
      <slot name="footer-left" />
      <el-button
        v-if="showCancelBtn"
        :loading="isCancelLoading"
        class="min-w-[96px]"
        style="color:#777c94"
        @click="handleCancelBtnClick"
      >{{ cancelBtnText }}</el-button>
      <el-button
        v-if="showSaveBtn"
        type="primary"
        class="min-w-[96px]"
        :loading="isLoading"
        @click="handleSaveBtnClick"
      >{{ saveBtnText }}</el-button>
    </footer>
  </el-dialog>
</template>
<script >
export default {
  props: {
    modal: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: "提 示",
      require: true,
    },
    value: {
      type: Boolean,
      default: false,
      require: true,
    },
    // 是否展示按钮
    showCancelBtn: {
      type: Boolean,
      default: true,
    },
    showClose: {
      type: Boolean,
      default: true,
    },
    showFootBtn: {
      type: Boolean,
      default: true,
      require: true,
    },
    saveBtnText: {
      type: String,
      default: "确定",
      require: true,
    },
    cancelBtnText: {
      type: String,
      default: "取消",
      require: true,
    },
    width: {
      type: String,
      default: "50%",
    },
    closeOnPressEscape: {
      type: Boolean,
      default: false,
    },
    // 点击遮罩层是否关闭
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },
    // 关闭弹框之前触发的回调
    beforeClose:{
      type:[Function,Boolean],
      default:false
    }
  },
  computed:{
    showSaveBtn(){
      return this.$listeners.confirm
    }
  },
  data() {
    return {
      dialogVisible: false,
      isLoading: false,
      isCancelLoading: false,
    };
  },
  watch: {
    value(newValue) {
      this.dialogVisible = newValue;
    },
  },
  created() {
    this.dialogVisible = this.value;
  },
  methods: {
    close(){
      this.dialogVisible = false
      this.$emit('input',this.dialogVisible)
    },
   
    async handleSaveBtnClick() {
      const loading = (status = true) => (this.isLoading = status);
      const fn = this.$listeners.confirm;
      loading();
      try {
        fn && (await fn(this.close));
      } finally {
        loading(false);
      }
    },

    async handleCloseIconClick(){
      if(typeof this.beforeClose === "function") {
        return await this.beforeClose( this.close)
      }else{
        this.close()
      }
    },

    async handleCancelBtnClick() {

      if(typeof this.beforeClose === "function") {
        return await this.beforeClose( this.close)
      }

      const loading = (status = true) => (this.isCancelLoading = status);
      const fn = this.$listeners.cancel;

      if (!fn) return this.close();
      loading();

      try {
        fn && (await fn(this.close));
      } finally {
        loading(false);
      }
    },
  },
};
</script>
<style lang="scss">
.zui-dialog{
  .el-dialog{
    border-radius: 8px;
    overflow: hidden;
    min-width: 360px;
    
    &__header{
      height: 48px;
      margin:0 !important;
      line-height: 48px;
      font-size: 16px;
      border-bottom: .5px solid #eaeaea;
      position: relative;
      padding: 0 24px;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #24262a;
      .close{
        width: 48px;
        text-align: center;
        position: absolute;
        font-weight: 700;
        padding-right: 24px;
        right: 0;
        top: 0;
        cursor: pointer;
        color: #bcbcbc;
      }
    }
    &__footer{
      border-top: .5px solid #eaeaea;
      height: 68px;
      display: flex;
      padding: 0 24px;
      align-items: center;
      justify-content: flex-end;
    }
  }
  
  
}
</style>