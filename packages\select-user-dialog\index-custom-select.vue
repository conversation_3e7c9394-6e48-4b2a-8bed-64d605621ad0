<template>
  <div @click="handleClick">
    <div class="custom-select-container" ref="container" :class="{radio:!this.multiple,disabled:disabled}">
      <div
        class="flex flex-wrap zui__scrollbar-y"
        style="max-height: 150px;overflow: auto;"
      >
        <template v-for="(list,field) in newSelectData">
          <detail-table-custom-select-tag
            v-for="item in list"
            :key="item.userId || item.id"
            :item="item"
            :class="{'mb-7px':isTagMargin}"
            :field="field"
          />
        </template>
      </div>

      <p v-show="!hidePlaceholder " class="placeholder">
        {{ parentProps.placeholder }}
      </p>

      <i v-show="!hideIcon" class="search"></i>
    </div>
  </div>
</template>
<script>
import detailTableCustomSelectTag from "./index-custom-select-tag.vue"
import mixins from "./mixins"
import { cloneDeep } from "~/utils/index"

export default {
  components: {
    detailTableCustomSelectTag
  },
  mixins: [mixins],
  props: {
    selectData: {
      type: Object,
      default: () => {}
    }
  },
  watch:{
    async newSelectData(){
      await this.$nextTick()
      const height = this.$refs['container'].clientHeight
      this.isTagMargin = height>40
    }
  },
  data () {
    return {
      newSelectData: {},
      isTagMargin:false
    }
  },
  computed: {
    hidePlaceholder () {
      const { user, dept, role } = this.newSelectData
      return user.length || dept.length || role.length
    },
    selectText () {
      if (this.multiple) return ""
      const data = this.selectData[this.parentProps.tabField]
      if (!data) return ""
      const firstData = data[0]
      if (!firstData) return ""
      return firstData.name || firstData.label || firstData.roleName
    },
    hideIcon() {
      const { user, dept, role } = this.newSelectData
      return user.length || dept.length || role.length
    }
  },
  created () {
    this.setNewSelectData(this.selectData)
  },
  methods: {
    setNewSelectData (data) {
      this.newSelectData = cloneDeep(data)
    },
    handleClick () {
      if(this.disabled) return 
      this.$parent.show = true
    },
    confirm (data) {
      this.setNewSelectData(data)
    },
    handleTagDelClick ({ userId, id, roleId }, key) {
      const { selectData } = this.$parent
      const { newSelectData, $appSelectUserRef } = this

      if (key === "user") {
        newSelectData.user = newSelectData[key].filter(
          (item) => item.userId !== userId
        )
      }

      if (key === "dept") {
        newSelectData.dept = newSelectData[key].filter(
          (item) => item.id !== id
        )
      }

      if (key === "role") {
        newSelectData.role = newSelectData[key].filter(
          (item) => item.roleId !== roleId
        )
      }

      Object.assign(selectData, cloneDeep(newSelectData))
      $appSelectUserRef.confirm()
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-select-container {
  border: 1px solid #eef0f4;
  border-radius: 8px;
  padding: 0 10px;
  padding-right: 1px;
  line-height: 40px;
  min-height: 40px;
  background: #fff;
  cursor: pointer;
  font-size: 14px;
  box-sizing: border-box;
  min-width: 280px;
  color: #c5c8cf;
  padding-top: 0;
  position: relative;
  box-sizing: border-box;
  &.disabled{
    cursor: initial;
  }
  .flex-wrap{
    padding-top: 7px;
  }
  &.radio{
    height: 40px;
  }
  .search{
    // background: url('/public/images/search.png') no-repeat center;
    height: 16px;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    background-size:80%;
  }
  .placeholder{
    position: absolute;
    left: 0;
    top: 0;
    
    padding-left: 10px;
  }
}
</style>