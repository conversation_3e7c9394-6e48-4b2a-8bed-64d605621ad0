import axios from "axios"
import oladingUI from "~/src"

import { showMessage } from "./toast";
const instance = axios.create({
  timeout: 60 * 1000 * 10,
});

instance.interceptors.request.use(
  config => {
    const { baseURL,token } = oladingUI.rootConfig
    const oToken = token || localStorage.token

    config.baseURL = baseURL
    config.headers["Authorization"] = oToken
    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

instance.interceptors.response.use(function (response) {
  if(!response.data.success) {
    showMessage(response.data.message,"error")
    return Promise.reject(response);
  }
  return response.data.data;
}, function (error) {
  if(error.code === "ECONNABORTED") {
    showMessage("请求超时","error")
  }
  return Promise.reject(error);
});

export const get = (api,params) => instance.get(api,{
  params
})

export const post = (api,params) => instance.post(api,params)