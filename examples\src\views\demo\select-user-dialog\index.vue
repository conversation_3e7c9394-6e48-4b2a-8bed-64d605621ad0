<template>
  <div>
   <div>
    <div>
      <div>
        <z-select-user-dialog v-model="show"  @confirm="confirm" :modalAppendToBody="true"/>
        <el-button @click="show=true" >打开选择角色 选择部门 选择人员 弹框</el-button>
      </div>
    </div>
   </div>

    <z-select-user-dialog tabField="user" :beforeUserApiRequest="beforeUserApiRequest" v-model="show7" reInit @confirm="confirm"  />
    <!-- <z-select-user-dialog tabField="user" value="true" v-if="show5" @close="show5=false" @confirm="confirm"  /> -->
    <el-button @click="handleDisabledClick">设置禁用人员</el-button>


    <z-select-user-dialog tabField="user" v-model="show5" reInit @confirm="confirm"  />
    <!-- <z-select-user-dialog tabField="user" value="true" v-if="show5" @close="show5=false" @confirm="confirm"  /> -->
    <el-button @click="show5=true">每次都是新打开 reInit</el-button>

    <z-select-user-dialog tabField="user" v-model="show6" refreshMember @confirm="confirm" />
    <el-button @click="show6=true">每次打开都重新加载人员 refreshMember</el-button>

    <z-select-user-dialog tabField="user" v-model="show1" @confirm="confirm" />
    <el-button @click="show1=true">打开选人弹框</el-button>

    <z-select-user-dialog tabField="dept" v-model="show2" @confirm="confirm"  />
    <el-button @click="show2=true">打开选部门弹框</el-button>

    <z-select-user-dialog tabField="role" v-model="show3" @confirm="confirm"  />
    <el-button @click="show3=true">打开选角色弹框</el-button>

    <z-select-user-dialog v-model="show4" active="dept" @confirm="confirm" />
    <el-button @click="show4=true">打开选/用户/部门弹框</el-button>

    <h2>多选 全部</h2>
    <z-select-user-dialog inputWidth="280px" input @confirm="confirm" />

    <h2>单选 全部</h2>
    <z-select-user-dialog inputWidth="280px" placeholder="请选择角色，人员，部门" :multiple="false" input @confirm="confirm" />
    
    <h2>单选user</h2>
    <z-select-user-dialog inputWidth="280px" :multiple="false" input tabField="user" @confirm="confirm" />

    <h2>单选dept</h2>
    <z-select-user-dialog inputWidth="280px" :multiple="false" input tabField="dept" @confirm="confirm" />
    
    <h2>单选role</h2>
    <z-select-user-dialog inputWidth="280px" :multiple="false" input tabField="role" @confirm="confirm" />

    <el-button type="primary" @click="handleSetValue" >点击填充</el-button>
    <z-select-user-dialog ref="select-user-dialog" inputWidth="280px" input @confirm="confirm" />

    <h2>全部可选，无视禁用属性</h2>
    <z-select-user-dialog scene="search" inputWidth="280px" input @confirm="confirm" />

    <el-button type="primary" @click="handleSetValue2" >点击填充,id已删除</el-button>
    <z-select-user-dialog ref="select-user-dialog2"  :disabled="true" inputWidth="280px" input @confirm="confirm" />

  </div>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      show1: false,
      show2: false,
      show3: false,
      show4: false,
      show5:false,
      show6:false,
      show7:false,
      disableds:[]
    };
  },
  methods: {
    beforeUserApiRequest(list){
      list.forEach(item=>{
        const list = [
        12789,
        209682,
        212989,
        212988,
        212777,
        212779,
        212781,
        212783,
        212785,
        212787,
        212927,
        212931,
        212933,
        212935,
        212937,
        212939,
        212941,
        212943,
        212945,
        212947,
        212949,
        212951,
        212953,
        212955,
        212957,
        212959,
        212961,
        212963,
        212966,
        212967,
        212969,
        212972,
        212973,
        212975,
        212977,
        212979,
        212981,
        212983,
        212985,
        212987,
        212991,
        212778,
        212780,
        212782,
        212788,
        212786,
        212784,
        212938,
        212942,
        212940,
        212944,
        212946,
        212950,
        212958,
        212960,
        212962,
        212976,
        212970,
        212792,
        212928,
        212936,
        212934,
        212948,
        212954,
        212952,
        212956,
        212968,
        212971,
        212965,
        212974,
        212978,
        212982,
        212984,
        212986,
        212990,
        212776,
        212930,
        212980,
        212964,
        211773,
        211777,
        211779,
        211781,
        211783,
        211785,
        211787,
        211789,
        211791,
        211794,
        211795,
        211799,
        212611,
        212613,
        212615,
        212617,
        212619,
        212621,
        212623,
        212725,
        212729,
        212731,
        212735,
        212737,
        212739,
        212741,
        212743,
        212745,
        212747,
        212749,
        212751,
        212753,
        212755,
        212758,
        212759,
        212762,
        212764,
        212765,
        212767,
        212770,
        212771,
        212773,
        212775,
        211772,
        211780,
        211796,
        211784,
        211778,
        211790,
        211793,
        211798,
        211803,
        212606,
        212608,
        212614,
        212616,
        212618,
        212622,
        212730,
        212736,
        212738,
        212763,
        212748,
        212772,
        212750,
        212768,
        211782,
        211786,
        211788,
        211792,
        211800,
        211776,
        212752,
        212754,
        212760,
        212756,
        212766,
        212761,
        212746,
        212744,
        212769,
        212742,
        212774,
        212757,
        211688,
        211689,
        211693,
        211695,
        211697,
        211699,
        211702,
        211704,
        211705,
        211707,
        211709,
        211711,
        211713,
        211715,
        211717,
        211720,
        211721,
        211723,
        211725,
        211727,
        211730,
        211731,
        211733,
        211735,
        211738,
        211739,
        211741,
        211743,
        211745,
        211747,
        211749,
        211751,
        211753,
        211755,
        211758,
        211759,
        211761,
        211763,
        211765,
        211767,
        211770,
        211771,
        211775,
        211722,
        211703,
        211728,
        211726,
        211701,
        211706,
        211716,
        211737,
        211734,
        211736,
        211764,
        211766,
        211769,
        211774,
        211696,
        211698,
        211700,
        211708,
        211710,
        211714,
        211729,
        211732,
        211744,
        211746,
        211748,
        211750,
        211752,
        211718,
        211757,
        211692,
        211719,
        211724,
        211756,
        211694,
        211740,
        211742,
        211760,
        211712,
        211762,
        211768,
        211754,
        211605,
        211609,
        211611,
        211613,
        211616,
        211617,
        211619,
        211621,
        211622,
        211623,
        211625,
        211627,
        211629,
        211631,
        211633,
        211635,
        211637,
        211639,
        211641,
        211644,
        211646,
        211647,
        211649,
        211651,
        211653,
        211655,
        211657,
        211658,
        211659,
        211661,
        211663,
        211665,
        211667,
        211669,
        211671,
        211673,
        211675,
        211677,
        211679,
        211681,
        211683,
        211685,
        211691,
        211640,
        211638,
        211645,
        211652,
        211654,
        211656,
        211608,
        211604,
        211610,
        211612,
        211672,
        211643,
        211620,
        211674,
        211676,
        211684,
        211615,
        211632,
        211628,
        211636,
        211650,
        211624,
        211630,
        211642,
        211648,
        211664,
        211666,
        211662,
        211670,
        211668,
        211680,
        211678,
        211614,
        211618,
        211626,
        211634,
        211682,
        211686,
        211687,
        211690,
        211660,
        211525,
        211527,
        211529,
        211531,
        211533,
        211535,
        211537,
        211539,
        211541,
        211544,
        211545,
        211547,
        211549,
        211551,
        211553,
        211555,
        211557,
        211559,
        211561,
        211564,
        211565,
        211567,
        211569,
        211571,
        211573,
        211575,
        211577,
        211579,
        211581,
        211583,
        211585,
        211588,
        211589,
        211591,
        211593,
        211595,
        211597,
        211599,
        211601,
        211603,
        211607,
        211554,
        211556,
        211558,
        211592,
        211563,
        211524,
        211526,
        211530,
        211528,
        211538,
        211543,
        211548,
        211552,
        211560,
        211562,
        211587,
        211596,
        211600,
        211540,
        211566,
        211568,
        211570,
        211572,
        211576,
        211534,
        211546,
        211590,
        211602,
        211598,
        211542,
        211606,
        211550,
        211578,
        211584,
        211574,
        211580,
        211582,
        211586,
        211532,
        211536,
        211594,
        211438,
        211439,
        211441,
        211443,
        211445,
        211448,
        211449,
        211451,
        211453,
        211455,
        211457,
        211459,
        211461,
        211463,
        211465,
        211467,
        211469,
        211471,
        211473,
        211475,
        211477,
        211479,
        211481,
        211483,
        211486,
        211487,
        211489,
        211491,
        211493,
        211495,
        211498,
        211499,
        211501,
        211503,
        211505,
        211507,
        211509,
        211512,
        211513,
        211515,
        211517,
        211520,
        211521,
        211523,
        211452,
        211454,
        211460,
        211464,
        211492,
        211482,
        211484,
        211490,
        211496,
        211436,
        211516,
        211514,
        211447,
        211450,
        211456,
        211458,
        211462,
        211466,
        211478,
        211472,
        211510,
        211506,
        211504,
        211508,
        211519,
        211437,
        211440,
        211446,
        211444,
        211476,
        211468,
        211474,
        211485,
        211488,
        211511,
        211497,
        211500,
        211502,
        211522,
        211470,
        211442,
        211494,
        211480,
        211518,
        211348,
        211349,
        211353,
        211355,
        211357,
        211359,
        211361,
        211363,
        211365,
        211367,
        211369,
        211371,
        211373,
        211376,
        211378,
        211379,
        211381,
        211383,
        211385,
        211388,
        211390,
        211391,
        211393,
        211395,
        211397,
        211399,
        211401,
        211403,
        211405,
        211407,
        211409,
        211411,
        211413,
        211415,
        211417,
        211420,
        211421,
        211423,
        211425,
        211427,
        211429,
        211431,
        211433,
        211435,
        211354,
        211356,
        211358,
        211362,
        211364,
        211366,
        211370,
        211372,
        211380,
        211375,
        211384,
        211386,
        211387,
        211389,
        211394,
        211398,
        211396,
        211410,
        211416,
        211418,
        211414,
        211422,
        211426,
        211377,
        211382,
        211392,
        211374,
        211402,
        211406,
        211428,
        211408,
        211412,
        211404,
        211424,
        211430,
        211432,
        211352,
        211434,
        211360,
        211368,
        211400,
        211419,
        211262,
        211264,
        211265,
        211268,
        211269,
        211271,
        211273,
        211276,
        211277,
        211279,
        211281,
        211283,
        211285,
        211288,
        211290,
        211292,
        211294,
        211295,
        211298,
        211299,
        211301,
        211303,
        211306,
        211307,
        211310,
        211311,
        211313,
        211315,
        211317,
        211319,
        211321,
        211323,
        211325,
        211327,
        211329,
        211331,
        211333,
        211335,
        211337,
        211339,
        211341,
        211344,
        211345,
        211351,
        211282,
        211291,
        211289,
        211287,
        211293,
        211260,
        211309,
        211272,
        211314,
        211270,
        211286,
        211305,
        211296,
        211302,
        211320,
        211300,
        211326,
        211322,
        211332,
        211336,
        211328,
        211334,
        211338,
        211342,
        211347,
        211340,
        211343,
        211261,
        211275,
        211278,
        211280,
        211304,
        211318,
        211312,
        211308,
        211324,
        211330,
        211266,
        211267,
        211274,
        211284,
        211297,
        211346,
        211350,
        211316,
        211169,
        211172,
        211175,
        211176,
        211178,
        211180,
        211182,
        211184,
        211186,
        211188,
        211190,
        211192,
        211194,
        211196,
        211198,
        211201,
        211202,
        211204,
        211206,
        211208,
        211210,
        211212,
        211214,
        211216,
        211218,
        211220,
        211223,
        211225,
        211227,
        211229,
        211230,
        211232,
        211234,
        211235,
        211236,
        211237,
        211239,
        211240,
        211241,
        211243,
        211245,
        211248,
        211247,
        211249,
        211251,
        211253,
        211254,
        211255,
        211256,
        211258,
        211259,
        211205,
        211207,
        211174,
        211177,
        211191,
        211189,
        211187,
        211213,
        211226,
        211231,
        211242,
        211252,
        211250,
        211179,
        211203,
        211183,
        211185,
        211209,
        211195,
        211193,
        211211,
        211215,
        211199,
        211222,
        211168,
        211221,
        211228,
        211233,
        211246,
        211244,
        211181,
        211257,
        211219,
        211263,
        211173,
        211224,
        211200,
        211238,
        211197,
        211217,
        211089,
        211090,
        211092,
        211094,
        211096,
        211098,
        211100,
        211102,
        211105,
        211106,
        211108,
        211110,
        211112,
        211115,
        211116,
        211118,
        211120,
        211122,
        211124,
        211126,
        211128,
        211130,
        211132,
        211134,
        211136,
        211138,
        211141,
        211143,
        211145,
        211146,
        211148,
        211150,
        211152,
        211154,
        211156,
        211158,
        211161,
        211163,
        211165,
        211166,
        211170,
        211088,
        211091,
        211093,
        211095,
        211103,
        211123,
        211117,
        211119,
        211151,
        211135,
        211155,
        211107,
        211111,
        211147,
        211149,
        211160,
        211127,
        211129,
        211097,
        211101,
        211121,
        211131,
        211137,
        211144,
        211157,
        211109,
        211113,
        211167,
        211171,
        211139,
        211159,
        211099,
        211125,
        211142,
        211153,
        211114,
        211104,
        211162,
        211164,
        211140,
        211133,
        211009,
        211010,
        211012,
        211014,
        211016,
        211018,
        211020,
        211022,
        211024,
        211026,
        211028,
        211031,
        211033,
        211035,
        211036,
        211039,
        211040,
        211043,
        211044,
        211047,
        211048,
        211050,
        211052,
        211054,
        211056,
        211058,
        211060,
        211062,
        211064,
        211066,
        211068,
        211070,
        211073,
        211074,
        211077,
        211078,
        211080,
        211082,
        211084,
        211087,
        211013,
        211021,
        211017,
        211067,
        211065,
        211069,
        211037,
        211034,
        211032,
        211019,
        211015,
        211029,
        211042,
        211045,
        211046,
        211049,
        211051,
        211053,
        211059,
        211061,
        211063,
        211071,
        211081,
        211079,
        211083,
        211085,
        211038,
        211023,
        211025,
        211027,
        211041,
        211057,
        211086,
        211075,
        211030,
        211008,
        211055,
        211076,
        211072,
        210932,
        210934,
        210936,
        210938,
        210940,
        210942,
        210944,
        210947,
        210948,
        210950,
        210953,
        210954,
        210956,
        210959,
        210961,
        210962,
        210964,
        210965,
        210966,
        210968,
        210971,
        210972,
        210975,
        210977,
        210979,
        210981,
        210982,
        210984,
        210987,
        210988,
        210990,
        210992,
        210993,
        210994,
        210996,
        210997,
        210998,
        211001,
        211002,
        211004,
        211006,
        210939,
        210933,
        210937,
        210946,
        210951,
        210945,
        210952,
        210980,
        211007,
        210976,
        210974,
        210949,
        210958,
        210969,
        210973,
        210978,
        210986,
        210983,
        210985,
        211003,
        211000,
        210943,
        210957,
        210970,
        210991,
        210999,
        210967,
        210989,
        210963,
        210935,
        210960,
        210995,
        210955,
        211011,
        210941,
        211005,
        210868,
        210872,
        210875,
        210877,
        210878,
        210880,
        210882,
        210883,
        210884,
        210886,
        210888,
        210890,
        210891,
        210892,
        210894,
        210896,
        210898,
        210900,
        210902,
        210904,
        210907,
        210908,
        210910,
        210913,
        210914,
        210916,
        210918,
        210921,
        210923,
        210924,
        210927,
        210928,
        210930,
        210876,
        210895,
        210899,
        210901,
        210915,
        210920,
        210926,
        210931,
        210903,
        210905,
        210874,
        210925,
        210869,
        210879,
        210887,
        210893,
        210929,
        210881,
        210889,
        210885,
        210917,
        210909,
        210911,
        210906,
        210922,
        210873,
        210897,
        210912,
        210919,
        210805,
        210809,
        210811,
        210813,
        210815,
        210817,
        210820,
        210821,
        210824,
        210825,
        210828,
        210830,
        210831,
        210833,
        210835,
        210837,
        210839,
        210841,
        210843,
        210845,
        210847,
        210849,
        210850,
        210852,
        210854,
        210855,
        210856,
        210858,
        210860,
        210861,
        210862,
        210864,
        210867,
        210870,
        210829,
        210834,
        210840,
        210846,
        210866,
        210848,
        210842,
        210812,
        210816,
        210827,
        210823,
        210808,
        210810,
        210819,
        210857,
        210863,
        210865,
        210818,
        210832,
        210844,
        210804,
        210814,
        210822,
        210859,
        210826,
        210838,
        210836,
        210851,
        210853,
        210871,
        210736,
        210737,
        210740,
        210742,
        210744,
        210746,
        210747,
        210749,
        210752,
        210753,
        210755,
        210757,
        210759,
        210761,
        210763,
        210765,
        210767,
        210770,
        210771,
        210773,
        210775,
        210777,
        210779,
        210781,
        210783,
        210785,
        210787,
        210790,
        210791,
        210793,
        210794,
        210795,
        210797,
        210799,
        210801,
        210803,
        210807,
        210758,
        210751,
        210756,
        210760,
        210738,
        210748,
        210762,
        210768,
        210764,
        210776,
        210778,
        210798,
        210802,
        210789,
        210786,
        210792,
        210788,
        210784,
        210796,
        210806,
        210800,
        210750,
        210769,
        210780,
        210754,
        210772,
        210739,
        210743,
        210766,
        210774,
        210782,
        210741,
        210745,
        210660,
        210662,
        210663,
        210664,
        210665,
        210668,
        210670,
        210671,
        210673,
        210675,
        210676,
        210678,
        210679,
        210681,
        210683,
        210686,
        210687,
        210689,
        210691,
        210693,
        210696,
        210698,
        210701,
        210702,
        210703,
        210704,
        210708,
        210709,
        210710,
        210711,
        210712,
        210713,
        210714,
        210715,
        210716,
        210719,
        210722,
        210723,
        210724,
        210725,
        210726,
        210727]
        if(list.includes(item.userId)) {
          console.log(123)
          item.enable = item.disabled
          item.disabled = true
        }
      })
      return list
    },
    handleDisabledClick(){
      this.show7 = true
    },
    confirm(params){
      console.log(params)
    },
    handleSetValue(){
      this.$refs["select-user-dialog"].setValue({
        // userId:[102723,102722],
        // roleId:[ 140, 142,1142 ],
        userId:[50247,11207]
        // roleId:[133], userId:[102723,102722,114115], deptId:[139302]
      })
    },
    handleSetValue2(){
      this.$refs["select-user-dialog2"].setValue({
        roleId:[6,211], userId:[17213,12789], deptId:[114115,114116]
      })
    }
  },
};
</script>

<style lang="scss" scoped>
  .el-button,h2{
    margin: 10px 0;
  }
</style>