<template>
  <div>
    <z-dialog v-model="show" @confirm="confirm" />
    
    <z-button @click.native="show=true" >打开弹框</z-button>
  </div>
</template>
<script>
export default {
  data() {
    return {
      show: false,
    };
  },
  methods:{
    confirm(close){
      return new Promise((res)=>{
        setTimeout(() => {
          res()
          close()
        }, 1000);
      })
    }
  }
};
</script>