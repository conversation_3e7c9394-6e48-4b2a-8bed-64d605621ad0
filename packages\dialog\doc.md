## 弹框组件

dialog 弹框统一封装，并做一些扩展功能.

### 基本用法

```html
<app-dialog v-model="show">
  <div>内容</div>
</app-dialog>

<script>
  export default {
    data() {
      return {
        show: false,
      }
    },
  }
</script>
```

展示底部取消按钮

```html
<app-dialog v-model="show" showCancelBtn>
  <div>内容</div>
</app-dialog>

<script>
  export default {
    data() {
      return {
        show: false,
      }
    },
  }
</script>
```

点击确认按钮，以后需要手动调用形参的 close 方法进行关闭

```html
<app-dialog v-model="show" @confirm="confirm">
  <div>内容</div>
</app-dialog>

<script>
  export default {
    data() {
      return {
        show: false,
      }
    },
    methods: {
      confirm({ loading, close }) {
        // 打开按钮loading状态（防止重复点击） ， 如果非请求API 无需打开loading
        loading()
        // 模拟API请求
        setTimeout(() => {
          console.log("请求完成")
        }, 1000)
        // 关闭按钮loading状态
        loading(false)
        // 最后关闭弹框
        close()
      },
    },
  }
</script>
```

### Props

默认支持 el-dialog 所有属性及方法。

| 参数                  | 说明                               | 类型    | 可选值 | 默认值 |
| --------------------- | ---------------------------------- | ------- | ------ | ------ |
| v-model               | 是否显示隐藏                       | boolean | -      | -      |
| titlel                | 标题名称                           | string  | -      | 提 示  |
| showCancelBtn         | 是否展示取消按钮                   | boolean | true   | false  |
| saveBtnDisabled       | 确定按钮是否禁用                   | boolean | true   | false  |
| showSaveBtn           | 是否展示保存按钮                   | boolean | true   | false  |
| showFootBtn           | 是否展示底部按钮                   | boolean | false  | true   |
| cancelBtnText         | 取消按钮文字                       | string  | -      | 取 消  |
| saveBtnText           | 确认按钮文字                       | string  | -      | 确 定  |
| close-on-click-modal  | 是否可以通过点击 modal 关闭 Dialog | boolean | —      | false  |
| close-on-press-escape | 是否可以通过按下 ESC 关闭 Dialog   | boolean | —      | false  |
| width                 | Dialog 的宽度                      | string  | —      | 50%    |
| modal                 | 是否需要遮罩层                     | boolean | —      | true   |

### Events

默认支持 el-dialog 所有事件

| 参数    | 说明             | 回调参数                        |
| ------- | ---------------- | ------------------------------- |
| confirm | 点击确定的回调   | close:Function,loading:Function |
| cancel  | 点击取消按钮回调 | close:Function                  |
