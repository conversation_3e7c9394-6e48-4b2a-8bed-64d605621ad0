import { defineConfig } from 'vite'
import { createVuePlugin } from 'vite-plugin-vue2'
import { join, resolve } from "path"
import Unocss from 'unocss/vite'
import presetWind from '@unocss/preset-wind'
import { presetUno, presetAttributify, presetIcons } from 'unocss'

const root = join(process.cwd(), "./examples")

// https://vitejs.dev/config/
export default defineConfig({
  root,
  plugins: [
    Unocss({
      presets: [
        presetWind(),
        presetUno(),
        presetAttributify(),
        presetIcons()
      ]
    }),
    createVuePlugin()
  ],
  resolve: {
    alias: {
      "~": resolve("./"),
      "@examplesView": resolve("./examples/src/views/"),
    }
  },
  server:{
    port:3009,
    host:"0.0.0.0"
  },
  build: {
    // cssCodeSplit: true,
    lib: {
      entry: resolve(__dirname, 'src/index.js'),
      name: 'oladingVue2Ui',
      fileName: 'index'
    },
    outDir: resolve("./lib"),
    rollupOptions: {
      external: ['vue', 'element-ui', 'axios','vue-virtual-scroll-list'],
      output: {
        exports: "named",
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          vue: 'Vue',
          'element-ui': 'ElementUI',
          'axios':'axios',
          'vue-virtual-scroll-list':'vue-virtual-scroll-list'
        }
      }
    }
  }
})
