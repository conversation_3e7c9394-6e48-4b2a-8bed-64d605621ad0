{"name": "olading-vue-zui", "version": "0.1.6", "description": "", "type": "module", "main": "lib/index.js", "files": ["lib", "src", "packages"], "scripts": {"dev": "vite", "build": "vite build --emptyOutDir", "add": "plop", "yalc": "zx ./yalc.mjs --name olading-business-ui2", "pub": "npm run  build && npm publish --registry=https://registry.npmjs.org/"}, "peerDependencies": {"element-ui": "^2.15.7", "vue": "2.6.11"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": [">0.2%", "last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@iconify-json/ep": "^1.1.8", "@iconify-json/fa6-solid": "^1.1.7", "@unocss/preset-attributify": "^0.46.5", "@unocss/preset-icons": "^0.46.5", "@unocss/preset-uno": "^0.46.5", "@unocss/preset-wind": "^0.46.5", "@unocss/reset": "^0.46.5", "@unocss/vite": "^0.46.5", "axios": "^1.1.3", "element-ui": "2.15.7", "plop": "^3.1.1", "sass": "^1.56.1", "unocss": "^0.46.5", "vite": "^3.2.3", "vite-plugin-vue2": "^2.0.2", "vue": "2.6.11", "vue-router": "3.5.3", "vue-template-compiler": "2.6.11"}, "dependencies": {"vue-virtual-scroll-list": "2.3.5"}}