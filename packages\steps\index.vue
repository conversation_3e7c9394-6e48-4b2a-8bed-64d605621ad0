<template>
  <div class="z-steps flex">
    <div v-for="(item,index) in newOptions" class="z-steps-box flex items-center justify-center" :class="{ active:index===active }">
      <i class="el-icon-circle-check mr-[6px]" v-show="active>index" />
      <span class="index rounded-full mr-[6px] flex items-center justify-center" v-show="index>=active" >{{index+1}}</span>
      <span>{{ item.title }}</span>
    </div>
  </div>
</template>

<script>

const normalizationPropsOptions = (options)=>{
  if(!Array.isArray(options)) throw new Error("options 参数不是一个数组")
  if(options.length<2) throw new Error("steps 组件，步骤不得少于 2 步。")
  let newOptions = []
  options.forEach(item=>{
    if(typeof item === "string") {
      newOptions.push({
        title:item
      })
    }else{
      newOptions.push(item)
    }
  })
  return newOptions
}

export default {
  name:"steps",
  props: {
    options: {
      type: Array,
      default: ()=>[],
      require: true,
    },
    active: {
      type: Number,
      default: 0,
    },
  },
  created(){
    this.newOptions = normalizationPropsOptions(this.options)
  }

}
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>
