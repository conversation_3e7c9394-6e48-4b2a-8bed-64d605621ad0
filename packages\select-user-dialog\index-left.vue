<template>
  <div
    class="index-left flex flex-col border-r-0.5px pl-16px"
  >
    <!-- 标题&tab切换 -->
    <div v-show="showLeftTitle">
      <div class="title py-16px">
        请选择筛选方式
      </div>
      <div class="tags pr-16px">
        <span
          v-for="item in newTabList"
          :key="item.value"
          class="text-14px font-medium inline-block py-6px px-12px mr-10px cursor-pointer rounded-14px"
          :class="{active:item.value === tabActive}"
          @click="handleTabClick(item)"
        >{{ item.label }}</span>
      </div>
    </div>
    <!-- 搜索输入框 -->
    <div
      class="search-input p-16px pl-0"
      :class="{ 'opacity-0': isLoading }"
      @keyup.enter.stop="noop"
    >
      <!-- 搜索输入框 -->
      <el-input
        v-model.trim="keyword"
        autocomplete="off"
        clearable
        suffix-icon="el-icon-search"
        :placeholder="inputPlaceholder[tabActive]"
      />
    </div>

    <!-- 内容 -->
    <div
      class="left-box zui__scrollbar-y"
      ref="left-box"
      v-loading="isLoading"
      :class="{'overflow-hidden':tabActive==='user' && !keyword }"
    >
      <!-- 指定用户选择 -->
      <index-left-user
        v-show="showBox(USER)"
        :class="{'opacity-0': isLoading}"
        ref="index-left-user"
        :options="deptTreeList"
        :userList="userList"
        :keyword="keyword"
      />

      <!-- 指定部门选择 -->
      <index-left-tree
        v-show="showBox(DEPARTMENT)"
        :class="{'opacity-0': isLoading}"
        ref="index-left-tree"
        :options="deptTreeList"
        :keyword="keyword"
      />

      <!--指定 角色选择 -->
      <index-left-role-list
        :class="{'opacity-0': isLoading}"
        v-show="showBox(ROLE)"
        ref="index-left-role-list"
        :roles="newRoles"
      />
    </div>

  </div>
</template>
<script>
import { getListMerchantMemberApi, getDepartmentTreeApi, geRoleListApi, } from "./api";
import indexLeftRoleList from "./index-left-role-list.vue";
import { tabList, DEPARTMENT, USER, ROLE } from "./config";
import indexLeftTree from "./index-left-tree.vue";
import indexLeftUser from "./index-left-user.vue";
import mixins from "./mixins";
import { debounce } from "~/utils/index"

const tabMethods = {
  // 获取 指定用户数据
  async getUserList() {
    await tabMethods.getDeptList.call(this)
  },
  // 获取指定部门数据
  async getDeptList() {
    try {
      this.deptTreeList = await getDepartmentTreeApi();
    } catch (err) {
      console.log(err)
      this.deptTreeList = [];
    }
  },
  // 获取指定角色数据
  async getRoleList() {
    try {
      this.roles = await geRoleListApi();
    } catch (err) {
      console.error(err);
      this.roles = [];
    }
  },
};

export default {
  components: {
    indexLeftRoleList,
    indexLeftTree,
    indexLeftUser,
  },
  mixins: [mixins],
  inject: ["parentProps","$appSelectUserRef"],
  provide() {
    return {
      showLeftTitle: this.showLeftTitle,
    };
  },
  data() {
    const { beforeUserApiRequest } = this.$appSelectUserRef

    // 根据关键字查询用户 , 防抖优化
    this.remoteSearchUserList = debounce(async()=>{
      const keyword = this.keyword.trim()
      if(!keyword) return this.userList = []
      const { list } = await getListMerchantMemberApi({
        start: 0,
        limit: 50,
        filters: {
          keywords:keyword
        }
      })

      if(typeof beforeUserApiRequest === "function") {
        this.userList = await beforeUserApiRequest(list)
        return 
      }

      this.userList = list
    },300)

    return {
      isLoading: false,
      DEPARTMENT,
      show: false,
      loadNum:0,
      USER,
      ROLE,
      keyword: "",
      tabList,
      roles: [],
      userList: [],
      deptTreeList: [],
    };
  },
  watch:{
    keyword(){
      if(this.$appSelectUserRef.tabActive === "user") {
        this.remoteSearchUserList()
      }
    },
    async "$appSelectUserRef.value" (newValue){
      if(!this.parentProps.refreshMember) return 

      if(!newValue) return this.deptTreeList = []
      this.$refs["index-left-user"].menusActive = []
      this.loadNum = 0
      await this.handleTabClick({
        value:this.tabActive
      })
    }
  },
  computed: {
    tabActive(){
      return this.$appSelectUserRef.tabActive
    },
    // 是否展示标题 ， 如果只有一个 tab 选项 ， 就不需要请选择筛选方式
    showLeftTitle() {
      const maxLength = 1;
      return this.newTabList.length > maxLength;
    },
    // 顶部tab筛选选项
    newTabList() {
      return tabList.filter((item) => {
        return this.parentProps.tabField.includes(item.value);
      });
    },
    // 切换不同状态， 展示不同的占位符
    inputPlaceholder() {
      return {
        [USER]: "请输入员工姓名",
        [DEPARTMENT]: "请输入部门名称",
        [ROLE]: "请输入角色名称",
      };
    },
    // 角色列表， 前端关键字检索
    newRoles() {
      if (!this.keyword) return this.roles;
      return this.roles.filter((item) => item.roleName.includes(this.keyword));
    },
  },
  async created() {
    this.handleTabClick({
      value: this.$appSelectUserRef.tabActive,
    });
  },
  methods: {
    noop(){
    },
    // 点击tab
    async handleTabClick(tabItem) {
      const selectTabValue = tabItem.value
      const methods = {
        user:"getUserList",
        dept:"getDeptList",
        role:"getRoleList",
      }
      this.keyword = "";
      const apiFn = tabMethods[methods[selectTabValue]];
      this.$appSelectUserRef.updateTabActive(selectTabValue)

      if (!apiFn) return;
      const leftBoxRef = this.$refs["left-box"];
      leftBoxRef && leftBoxRef.scrollTo(0, 0);

      try {
        this.isLoading = true;
        await apiFn.call(this);
      } finally {
        if(selectTabValue!=="user") {
          this.isLoading = false;
        }
        if(selectTabValue === "user" && this.loadNum>0) {
          this.isLoading = false;
        }
        this.loadNum++
      }
    },
    // 是否显示下边 选择部门... 内容盒子
    showBox(id) {
      return id === this.$appSelectUserRef.tabActive;
    },
    getRef(referenceName) {
      return this.$refs[referenceName];
    },
    clearKeyword() {
      this.keyword = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.index-left {
  display: flex;
  flex-direction: column;
  height: 400px;
  border-color: #eaeaea !important;
  .left-box {
    flex: 1;
    position: relative;
  }
  .tags {
    cursor: pointer;
    span {
      font-family: PingFangSC-Medium;
      color: #6a6f7f;
      letter-spacing: 0;
      display: inline-block;
      background: #edf1f4;
      &.active,
      &:active {
        background: #ecf1ff;
      }
    }
  }
  ::v-deep .el-loading-mask {
    background: #fff;
    .el-loading-spinner .circular {
      display: inline-block;
    }
  }
}
</style>