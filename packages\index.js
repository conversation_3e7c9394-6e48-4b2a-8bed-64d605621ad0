/*
 * @Date: 2022-11-14 12:21:07
 * @LastEditors: zhaoxm
 * @LastEditTime: 2022-11-15 21:49:40
 * @Description: 导出组件
 */

import { rootConfig } from "../src/config"

const componentModules = import.meta.globEager("./*/index.vue")

// 依次给组件注册install , 支持按需引入
const install = (componentName,componentInstance)=>{
  componentInstance.install = (app)=> {
    app.component(rootConfig.uiPrefix+componentName,componentInstance)
  }
}

const components = Object.keys(componentModules).reduce((modules, path) => {
  const componentName = path.substring(2).replace("/index.vue","")
  modules[componentName] = componentModules[path].default
  install(componentName,modules[componentName])
  return modules
}, {})

export default {
  ...components
}