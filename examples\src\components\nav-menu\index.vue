<template>
  <el-menu
    background-color="#545c64"
    text-color="#fff"
    router
    active-text-color="#ffd04b"
    :default-openeds="defaultOpeneds"
    :default-active="defaultActive"
  >
    <el-menu-item
      :index="item.path"
      v-for="(item,index) in routerList"
      :key="index"
    >
      <template slot="title">
        <!-- <i class="el-icon-message"></i> -->
        <span>{{item.meta.title}}</span>
      </template>
    </el-menu-item>
  </el-menu>
</template>
<script>
export default {
  data() {
    return {
      defaultOpeneds: [],
      defaultActive: "",
    };
  },
  computed: {
    routerList() {
      const { routes } = this.$router.options;
      return routes;
    },
  },
  watch: {
    $route(newValue) {
      this.defaultActive = newValue.path;
    },
  },
};
</script>