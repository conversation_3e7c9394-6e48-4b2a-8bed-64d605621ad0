<template>
  <div>
    <li
      v-if="!source.userId"
      class="flex"
      @click="handleItemClick(source,index)"
    >
    <div class="oi-icon_file1 text-16px olading-iconfont text-gray-400 mr-8px"></div>
      <div class="flex-1 flex">
        <z-omit-words-tooltip :content="source.name "
          style="width:0"
          class="flex-1"
          >{{source.name}}</z-omit-words-tooltip
        >
        <span class="member-count">（{{ source.descendantMemberCount }}人）</span>
      </div>
      <span class="next">下级</span>
    </li>

    <li
      v-else
      class="flex person "
      @click.stop.prevent="handlePersonClick(source)"
    >
      <div class="flex items-center">
        <!-- 用户设置了多选 -->
        <template v-if="multiple">
          <el-checkbox
            :value="selectCheckbox(source)"
            :disabled="isDisabled(source) "
          >
            <span />
          </el-checkbox>
        </template>

        <!-- 启用单选状态按钮 -->
        <template v-else>
          <el-radio
            :value="selectCheckbox(source)"
            :label="source.userId"
            :disabled="isDisabled(source)"
          >
            <span />
          </el-radio>
        </template>

        <div class="oi-icon_single olading-iconfont text-15px text-gray-400 mr-4px"></div>
        <z-omit-words-tooltip :content="source.name "
          style="width:230px"
          class="flex-1"
          >{{source.name}}</z-omit-words-tooltip
        >
        <!-- <span class="flex-1">{{ source.name }} </span> -->
      </div>
    </li>
  </div>
</template>

<script>
import mixins from "./mixins"
export default {
  name: "item-component",
  mixins: [mixins],
  props: {
    index: {
      default:0,
      type: Number,
    },
    source: {
      // here is: {uid: 'unique_1', text: 'abc'}
      type: Object,
      default () {
        return {}
      },
    },
    select: {
      type: Function,
      default: () => {},
    },
  },
  data () {
    return {
      newUserList: [],
      $parentRef: null,
    }
  },
  created () {
    const $parentReference = (this.$parentRef = this.$parent.$parent.$parent)
    this.newUserList = $parentReference.newUserList
  },
  methods: {
    handlePersonClick (...argument) {
      this.$appSelectUserRef.childIndexLeftRef(
        "index-left-user",
        "handlePersonClick",
        ...argument
      )
    },
    selectCheckbox (...argument) {
      return this.$parent.$parent.$parent.selectCheckbox(...argument)
    },
    handleItemClick (...argument) {
      this.$appSelectUserRef.childIndexLeftRef(
        "index-left-user",
        "handleItemClick",
        ...argument
      )
    },
    handleAllSelectClick () {
      this.$appSelectUserRef.childIndexLeftRef(
        "index-left-user",
        "handleAllSelectClick"
      )
    },
    isDisabled(source){
      if(this.$appSelectUserRef.scene === 'search') return false
      return !source.enable || source.disabled
    }
  },
}
</script>