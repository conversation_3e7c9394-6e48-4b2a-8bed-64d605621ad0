<template>
  <button
    class="border-1 px-10 rounded-2 leading-12"
    :class="[buttonStyle]"
    @click="onClick"
  >
    <slot />
  </button>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: "primary",
    },
  },
  computed: {
    buttonStyle() {
      const map = {
        "ou-button--primary": `
          bg-blue-500
          text-white
          border-blue-500
          hover:bg-blue-600
          hover:border-blue-700
          `,
        "ou-button--info": `
          bg-gray-500
          text-white
          border-gray-500
          hover:bg-gray-600
          hover:border-gray-700
        `,
        "ou-button--warning": `
          bg-yellow-500
          text-white
          border-yellow-500
          hover:bg-yellow-600
          hover:border-yellow-600
        `,
        "ou-button--danger": `
          bg-red-500
          text-white
          border-red-500
          hover:bg-red-600
          hover:border-red-600
        `,
      };
      return map[`ou-button--${this.type}`];
    },
  },
  methods: {
    onClick(ev) {
      this.$emit("click", ev);
    },
  },
};
</script>
